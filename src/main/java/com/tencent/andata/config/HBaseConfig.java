package com.tencent.andata.config;

import lombok.Data;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.AsyncConnection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;


import java.util.concurrent.ExecutionException;

@Data
@org.springframework.context.annotation.Configuration
@ConfigurationProperties(prefix = "hbase")
public class HBaseConfig {
    private String zookeeperQuorum;
    private String zookeeperZnodeParent;
    private int asyncTimeout = 300; // 默认120秒

    @Bean
    public AsyncConnection asyncHBaseConnection() throws ExecutionException, InterruptedException {
        Configuration config = HBaseConfiguration.create();
        config.set("hbase.zookeeper.quorum", zookeeperQuorum);
        config.set("hbase.zookeeper.property.clientPort", "2181"); // 默认端口或从属性中获取
        config.set("zookeeper.znode.parent", zookeeperZnodeParent);
        return ConnectionFactory.createAsyncConnection(config).get();
    }
}