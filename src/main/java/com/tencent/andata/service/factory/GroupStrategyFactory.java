package com.tencent.andata.service.factory;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.chunk.ChunkType;
import com.tencent.andata.smart.strategy.chunk.SpliceType;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import com.tencent.andata.smart.utils.TicketOperationUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Arrays;

/**
 * KA大客户群工单场景策略工厂
 */
public class GroupStrategyFactory extends AbstractStrategyFactory {
    private static final int SERVICE_CHANNEL_TARGET = 27;
    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final String STRATEGY_NAME = "KA大客户群工单结单质检";

    private static final Chunk KA_GROUP_CHUNK = Chunk.builder()
            .type(ChunkType.Composite)
            .maxSize(32 * 1024)
            .delimiter("\n")
            .conversationSpliceType(SpliceType.TicketQualityInspection).build();

    public GroupStrategyFactory(DatabaseConf databaseConf) {
        super(databaseConf);
    }


    @Override
    public Strategy createStrategy(String id) throws Exception {
        logger.info("开始处理Group策略，ID: " + id);

        JsonNode closedTicketOperation = TicketOperationUtils.getClosedTicketOperation(Long.valueOf(id));

        // 处理数据
        JsonNode[] processedData = processServiceChannelData(closedTicketOperation);

        // 创建带Trigger的Strategy
        Strategy.StrategyBuilder builder = Strategy.builder()
                .name(STRATEGY_NAME)
                .scene(Scene.Ticket) // 通过工单流水触发，所以还是Ticket
                .sceneIdentify(id)
                .chunk(KA_GROUP_CHUNK);

        // 只有当processedData不为空时才设置trigger
        if (processedData.length > 0) {
            Trigger trigger = Trigger.builder()
                    .type(TriggerType.Immediately)
                    .data(processedData)
                    .build();
            builder.trigger(trigger);
        }

        Strategy strategy = builder.build();
        logger.info("创建的Group Strategy对象: " + mapper.writeValueAsString(strategy));
        return strategy;
    }

    @Override
    protected JsonNode[] afterProcessServiceChannelData(JsonNode[] processedData) {
        // 处理需要调整时间的节点
        Arrays.stream(processedData)
                .filter(node -> node.get("service_channel").asInt() == SERVICE_CHANNEL_TARGET)
                .forEach(node -> {
                    // 直接获取毫秒时间戳
                    long startTime = node.get("start_time").asLong();
                    // 减去7天的毫秒数 (7 * 24 * 60 * 60 * 1000)
                    long adjustedTime = startTime - (7 * 24 * 60 * 60 * 1000L);
                    // 更新节点中的时间戳
                    ((ObjectNode) node).put("start_time", adjustedTime);
                });

        return processedData;
    }
}