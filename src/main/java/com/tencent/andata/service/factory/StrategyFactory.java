package com.tencent.andata.service.factory;

import com.tencent.andata.smart.strategy.model.Strategy;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 策略工厂接口
 * 用于创建不同渠道的Strategy对象
 */
public interface StrategyFactory {
    /**
     * 创建策略对象
     *
     * @param id 会话ID
     * @return 策略对象
     * @throws Exception 创建过程中可能发生的异常
     */
    Strategy createStrategy(String id) throws Exception;
    
    /**
     * 处理服务渠道数据（如果需要）
     * 
     * @param data 输入数据
     * @return 处理后的数据
     * @throws Exception 处理过程中可能发生的异常
     */
    default JsonNode[] processServiceChannelData(JsonNode data) throws Exception {
        return new JsonNode[0];
    }
} 