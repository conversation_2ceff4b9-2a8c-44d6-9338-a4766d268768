package com.tencent.andata.service.factory;

import com.tencent.andata.constant.ConversationConstants;
import com.tencent.andata.utils.struct.DatabaseConf;

/**
 * 策略工厂生成器
 * 用于根据渠道类型创建对应的策略工厂
 */
public class StrategyFactoryProducer {
    
    /**
     * 根据渠道类型获取对应的策略工厂
     *
     * @param channel 渠道类型
     * @param databaseConf 数据库配置
     * @return 对应的策略工厂
     * @throws IllegalArgumentException 当渠道类型无效时抛出
     */
    public static StrategyFactory getFactory(String channel, DatabaseConf databaseConf) {
        String lowerChannel = channel.toLowerCase();
        
        switch (lowerChannel) {
            case ConversationConstants.CHANNEL_WEBIM:
                return new WebIMStrategyFactory();
            case ConversationConstants.CHANNEL_TICKET:
                return new TicketStrategyFactory(databaseConf);
            case ConversationConstants.CHANNEL_GROUP:
                return new GroupStrategyFactory(databaseConf);
            default:
                throw new IllegalArgumentException(String.format(ConversationConstants.ERROR_INVALID_CHANNEL, channel));
        }
    }
} 