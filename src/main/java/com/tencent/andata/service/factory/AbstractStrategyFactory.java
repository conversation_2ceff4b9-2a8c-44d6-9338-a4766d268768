package com.tencent.andata.service.factory;

import com.tencent.andata.bean.SimpleCollector;
import com.tencent.andata.smart.access.operators.ServiceChannelProcess;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

import java.util.List;
import java.util.Objects;

/**
 * 策略工厂的抽象基类，封装公共逻辑
 */
public abstract class AbstractStrategyFactory implements StrategyFactory {
    protected final DatabaseConf databaseConf;
    private final Configuration flinkConfig;

    protected AbstractStrategyFactory(DatabaseConf databaseConf) {
        this.databaseConf = Objects.requireNonNull(databaseConf, "databaseConf cannot be null");
        this.flinkConfig = new Configuration();
    }

    /**
     * 处理服务渠道数据的模板方法
     */
    @Override
    public JsonNode[] processServiceChannelData(JsonNode data) throws Exception {
        validateInput(data);

        ServiceChannelProcess processor = new ServiceChannelProcess(databaseConf);
        try {
            processor.open(flinkConfig);
            JsonNode[] processedData = convertToArray(processWithCollector(processor, data));
            return afterProcessServiceChannelData(processedData);
        } finally {
            closeProcessor(processor);
        }
    }

    /**
     * 验证输入数据
     */
    private void validateInput(JsonNode data) {
        if (data == null || data.isNull()) {
            throw new IllegalArgumentException("Input data cannot be null");
        }
    }

    /**
     * 使用收集器处理数据
     */
    private List<JsonNode> processWithCollector(ServiceChannelProcess processor, JsonNode data) throws Exception {
        SimpleCollector collector = new SimpleCollector();
        processor.processElement(data, null, collector);
        return collector.getCollectedNodes();
    }

    /**
     * 将处理后的节点列表转换为数组
     */
    private JsonNode[] convertToArray(List<JsonNode> processedNodes) {
        if (processedNodes == null || processedNodes.isEmpty()) {
            return new JsonNode[0];
        }
        return processedNodes.toArray(new JsonNode[0]);
    }

    /**
     * 安全关闭处理器
     */
    private void closeProcessor(ServiceChannelProcess processor) {
        if (processor != null) {
            try {
                processor.close();
            } catch (Exception e) {
                // 记录关闭异常但不抛出，避免掩盖原始异常
                System.err.println("Error closing ServiceChannelProcess: " + e.getMessage());
            }
        }
    }

    /**
     * 子类可以重写此方法来实现特定的数据处理逻辑
     * 在标准服务渠道数据处理完成后执行的后处理钩子
     * @param processedData 处理后的数据
     * @return 最终处理结果
     */
    protected JsonNode[] afterProcessServiceChannelData(JsonNode[] processedData) {
        return processedData != null ? processedData : new JsonNode[0];
    }
}