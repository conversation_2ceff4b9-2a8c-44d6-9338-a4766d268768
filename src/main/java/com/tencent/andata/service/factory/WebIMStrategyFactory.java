package com.tencent.andata.service.factory;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.config.ChunkConfig;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.enums.Scene;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

/**
 * WebIM场景策略工厂
 */
public class WebIMStrategyFactory implements StrategyFactory {
    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final String STRATEGY_NAME = "WebIM结单质检";

    @Override
    public Strategy createStrategy(String id) throws Exception {
        try {
            Strategy strategy = Strategy.builder()
                    .name(STRATEGY_NAME)
                    .scene(Scene.WebIM)
                    .sceneIdentify(id)
                    .chunk(ChunkConfig.WebIMReply)
                    .build();

            logger.info("创建的WebIM Strategy对象: " + mapper.writeValueAsString(strategy));
            return strategy;
        } catch (Exception e) {
            logger.error("创建WebIM Strategy时发生异常: " + e.getMessage());
            // 如果创建失败，返回一个最简单的Strategy对象
            return Strategy.builder()
                    .name(STRATEGY_NAME)
                    .scene(Scene.WebIM)
                    .sceneIdentify(id)
                    .build();
        }
    }
} 