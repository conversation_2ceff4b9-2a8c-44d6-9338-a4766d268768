package com.tencent.andata.service.factory;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.config.ChunkConfig;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.chunk.Chunk;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.trigger.Trigger;
import com.tencent.andata.smart.strategy.trigger.TriggerType;
import com.tencent.andata.smart.utils.TicketOperationUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 工单场景策略工厂
 */
public class TicketStrategyFactory extends AbstractStrategyFactory {

    private static final int CALL_CENTER_SERVICE_CHANNEL = 2;
    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final String STRATEGY_NAME = "工单结单质检";

    public TicketStrategyFactory(DatabaseConf databaseConf) {
        super(databaseConf);
    }

    @Override
    public Strategy createStrategy(String id) throws Exception {
        logger.info("开始处理工单策略，ID: " + id);
        JsonNode closedTicketOperation = TicketOperationUtils.getClosedTicketOperation(Long.valueOf(id));
        JsonNode[] processedData = processServiceChannelData(closedTicketOperation);
        int serviceChannel = processedData[0].get("service_channel").asInt();
        Chunk chunk = getChunkByServiceChannel(serviceChannel);

        Trigger trigger = Trigger.builder()
                .type(TriggerType.Immediately)
                .data(processedData)
                .build();

        return Strategy.builder()
                .name(STRATEGY_NAME)
                .scene(Scene.Ticket)
                .sceneIdentify(id)
                .trigger(trigger)
                .chunk(chunk)
                .build();
    }

    private Chunk getChunkByServiceChannel(int serviceChannel) {
        if (serviceChannel == CALL_CENTER_SERVICE_CHANNEL) {
            return ChunkConfig.CallCenterReply;
        }

        return ChunkConfig.TicketExternalReplyChunk;
    }

}