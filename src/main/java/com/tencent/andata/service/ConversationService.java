package com.tencent.andata.service;

import com.tencent.andata.bean.ConversationResponse;
import com.tencent.andata.config.HBaseConfig;
import com.tencent.andata.constant.ConversationConstants;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.service.factory.StrategyFactory;
import com.tencent.andata.service.factory.StrategyFactoryProducer;
import com.tencent.andata.smart.etl.process.StrategyChunkBuildConversationAsyncProcess;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.util.Collection;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.springframework.stereotype.Service;

/**
 * 会话服务类
 * 负责处理会话相关的业务逻辑
 */
@Service
@RequiredArgsConstructor
public class ConversationService {

    private static final FlinkLog logger = FlinkLog.getInstance();

    private final HBaseConfig hbaseConfig;

    private StrategyChunkBuildConversationAsyncProcess asyncProcess;

    private DatabaseConf databaseConf;

    /**
     * 初始化异步处理器
     */
    @PostConstruct
    public void init() {
        Try.of(this::initializeAsyncProcess)
                .onFailure(e -> {
                    logger.error(ConversationConstants.LOG_INIT_FAILED, String.valueOf(e));
                    throw new RuntimeException(ConversationConstants.LOG_INIT_FAILED, e);
                })
                .onSuccess(__ -> logger.info(ConversationConstants.LOG_INIT_SUCCESS));
    }


    /**
     * 处理会话请求
     *
     * @param channel 渠道类型
     * @param id 会话ID
     * @return 会话响应的CompletableFuture
     */
    public CompletableFuture<ConversationResponse> processConversation(String channel, String id) {
        logger.info("接收到会话请求: channel=" + channel + ", id= " + id);
        CompletableFuture<ConversationResponse> responseFuture = new CompletableFuture<>();

        Try.of(() -> {
                    // 使用工厂生成器获取对应的策略工厂
                    StrategyFactory factory = StrategyFactoryProducer.getFactory(channel, databaseConf);
                    // 创建策略
                    return factory.createStrategy(id);
                })
                .onSuccess(strategy -> processStrategyAsync(strategy, responseFuture))
                .onFailure(e -> {
                    logger.error("处理请求失败: " + e.getMessage());
                    completeWithError(responseFuture, 500,
                            String.format(ConversationConstants.ERROR_PROCESS_REQUEST, e.getMessage()));
                });

        return responseFuture;
    }



    /**
     * 初始化异步处理器
     */
    private StrategyChunkBuildConversationAsyncProcess initializeAsyncProcess() throws Exception {
        Configuration config = new Configuration();
        asyncProcess = new StrategyChunkBuildConversationAsyncProcess(
                hbaseConfig.getZookeeperQuorum(),
                hbaseConfig.getZookeeperZnodeParent()
        );
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        databaseConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.mysql.work")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));
        asyncProcess.open(config);
        return asyncProcess;
    }


    /**
     * 异步处理策略
     */
    private void processStrategyAsync(Strategy strategy, CompletableFuture<ConversationResponse> responseFuture) {
        ResultFuture<Strategy> resultFutureAdapter = createResultFutureAdapter(strategy, responseFuture);
        asyncProcess.asyncInvoke(strategy, resultFutureAdapter);
    }

    /**
     * 创建ResultFuture适配器
     */
    private ResultFuture<Strategy> createResultFutureAdapter(Strategy strategy,
            CompletableFuture<ConversationResponse> responseFuture) {
        return new ResultFuture<Strategy>() {
            @Override
            public void complete(Collection<Strategy> result) {
                handleAsyncResult(result, strategy, responseFuture);
            }

            @Override
            public void completeExceptionally(Throwable error) {
                logger.error(ConversationConstants.ERROR_PROCESS_RESULT, String.valueOf(error));
                completeWithError(responseFuture, 500,
                        String.format(ConversationConstants.ERROR_PROCESS_RESULT, error.getMessage()));
            }
        };
    }

    /**
     * 处理异步结果 - 使用函数式方式消除if-else
     */
    private void handleAsyncResult(Collection<Strategy> result, Strategy strategy,
            CompletableFuture<ConversationResponse> responseFuture) {
        logger.info("开始处理异步结果, 结果集大小: " + (result != null ? result.size() : 0));

        try {
            Option.of(result)
                    .filter(r -> !r.isEmpty())
                    .flatMap(r -> {
                        Strategy firstStrategy = r.iterator().next();
                        if (firstStrategy == null) {
                            return Option.none();
                        }

                        if (firstStrategy.chunk == null) {
                            logger.warn("Strategy.chunk为null");
                            return Option.none();
                        }

                        String conversation = firstStrategy.chunk.conversation;
                        logger.info("获取到conversation: " + conversation);

                        return Option.of(conversation);
                    })
                    .fold(
                            () -> {
                                // 处理空结果的情况
                                String errorMsg = result == null || result.isEmpty()
                                        ? ConversationConstants.ERROR_EMPTY_RESULT
                                        : ConversationConstants.ERROR_GET_CONVERSATION;

                                logger.warn(String.format(ConversationConstants.LOG_GET_CONVERSATION_FAILED,
                                        strategy.scene, strategy.sceneIdentify));

                                return completeWithError(responseFuture, 404, errorMsg);
                            },
                            conversation -> {
                                // 处理成功的情况
                                logger.info(String.format(ConversationConstants.LOG_GET_CONVERSATION_SUCCESS,
                                        strategy.scene, strategy.sceneIdentify, conversation));

                                responseFuture.complete(ConversationResponse.success(conversation));
                                return responseFuture;
                            }
                    );
        } catch (Exception e) {
            logger.error("处理异步结果时发生异常: " + e.getMessage());
            completeWithError(responseFuture, 500, "处理异步结果时发生内部错误: " + e.getMessage());
        }
    }

    /**
     * 完成Future并返回错误响应
     */
    private CompletableFuture<ConversationResponse> completeWithError(CompletableFuture<ConversationResponse> future,
            int code, String errInfo) {
        future.complete(ConversationResponse.error(code, errInfo));
        return future;
    }

    /**
     * 销毁资源
     */
    @PreDestroy
    public void destroy() {
        Try.run(() -> {
            if (asyncProcess != null) {
                asyncProcess.close();
                logger.info(ConversationConstants.LOG_CLOSE_SUCCESS);
            }
        }).onFailure(e -> logger.error(ConversationConstants.LOG_CLOSE_FAILED, String.valueOf(e)));
    }
}