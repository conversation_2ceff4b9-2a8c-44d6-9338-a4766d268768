package com.tencent.andata.service;

import static com.tencent.andata.smart.strategy.enums.Scene.Ticket;
import static com.tencent.andata.smart.strategy.enums.Scene.WebIM;
import static com.tencent.andata.utils.JsonNodeUtils.removeFlowFields;

import com.tencent.andata.bean.SimilarConversationResponse;
import com.tencent.andata.bean.SimpleCollector;
import com.tencent.andata.config.HBaseConfig;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.access.operators.ServiceSceneProcess;
import com.tencent.andata.smart.strategy.Strategy;
import com.tencent.andata.smart.strategy.chunk.model.HbaseTable;
import com.tencent.andata.smart.strategy.chunk.source.ConversationSource;
import com.tencent.andata.smart.strategy.chunk.source.HbasePrefixOperationSource;
import com.tencent.andata.smart.strategy.enums.Scene;
import com.tencent.andata.stats.FlowQueryResult;
import com.tencent.andata.stats.PerformanceStats;
import com.tencent.andata.stats.QueryResult;
import com.tencent.andata.stats.QueryStatusSummary;
import com.tencent.andata.utils.JsonNodeUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.lookup.exception.QueryException;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.processor.ProcessorFactory;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.hbase.client.AsyncConnection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SimilarConversationService {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final Logger log = Logger.getLogger(SimilarConversationService.class);

    // 数据库配置
    private DatabaseConf databaseConf;
    private DatabaseConf pgdatabaseConf;
    private DatabaseConf dwdDbConf;
    private DatabaseConf messageDbConf;
    private DatabaseConf toolDbConf;
    private DatabaseConf starocksDbConf;

    // SQL构建器
    private JDBCSqlBuilderImpl T201TableBuilder;
    private JDBCSqlBuilderImpl T204TableBuilder;
    private JDBCSqlBuilderImpl GroupTableBuilder;  //一事一群
    private JDBCSqlBuilderImpl AiTableBuilder; //智能客服
    private JDBCSqlBuilderImpl ExternGroupTableBuilder; //外部群
    private JDBCSqlBuilderImpl TicketGroupTableBuilder; //外部群
    private JDBCSqlBuilderImpl StarRocksTableBuilder; //starock

    // 查询对象
    private HashMapJDBCLookupQuery t204Query;
    private HashMapJDBCLookupQuery t201Query;
    private HashMapJDBCLookupQuery oneGroupQuery;
    private HashMapJDBCLookupQuery aiQuery;
    private HashMapJDBCLookupQuery externGroupQuery;
    private HashMapJDBCLookupQuery ticketGroupQuery;
    private HashMapJDBCLookupQuery starockQuery;

    private ObjectMapper mapper;
    private final HBaseConfig hbaseConfig;

    // HBase相关
    private Map<Scene, HbaseTable> TableMap;
    private ExecutorService executorService;
    private ConversationSource conversationSource;
    private AsyncConnection asyncConnection;

    // 预初始化的处理器
    private ServiceSceneProcess sceneProcessor;
    private Configuration config;
    private ProcessorFactory processorFactory;

    // 线程安全的收集器（优化：使用对象池减少创建开销）
    private ThreadLocal<SimpleCollector> sceneCollectorThreadLocal = ThreadLocal.withInitial(() -> {
        SimpleCollector collector = new SimpleCollector();
        // 预初始化收集器，避免运行时初始化开销
        return collector;
    });

    private static final Integer ZERO_POISION = 0;


    private void initHBaseConnection() throws Exception {
        org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
        conf.set("hbase.zookeeper.quorum", hbaseConfig.getZookeeperQuorum());
        conf.set("zookeeper.znode.parent", hbaseConfig.getZookeeperZnodeParent());

        asyncConnection = ConnectionFactory.createAsyncConnection(conf).get();
        // 优化线程池：使用CPU核心数的2倍提高并发处理能力
        int threadPoolSize = Math.max(4, Runtime.getRuntime().availableProcessors() * 2);
        executorService = Executors.newScheduledThreadPool(threadPoolSize);

        HbaseTable wbimTable = new HbaseTable("Webim_operation", "cf", "data");
        HbaseTable ticketTable = new HbaseTable("Incident_ticket_operation", "cf", "data");

        TableMap = new HashMap<>();
        TableMap.put(Scene.WebIM, wbimTable);
        TableMap.put(Scene.Ticket, ticketTable);
    }

    @PostConstruct
    public void init() throws Exception {
        // 初始化基础配置
        Properties properties = PropertyUtils.loadProperties("env.properties");
        initHBaseConnection();
        this.conversationSource = new HbasePrefixOperationSource(asyncConnection, TableMap, executorService);
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        mapper = new ObjectMapper();

        T201TableBuilder = JDBCSqlBuilderImpl.builder()
                .tableName("t201_ticket")
                .selectAll()
                .conditionKeyList(Arrays.asList("ticket_id"))
                .databaseEnum(DatabaseEnum.MYSQL)
                .limit(1);

        T204TableBuilder = JDBCSqlBuilderImpl.builder()
                .tableName("t204_ticket_operation_extra")
                .selectField(Arrays.asList("mc_conversation_id"))
                .conditionKeyList(Arrays.asList("ticket_id"))
                .databaseEnum(DatabaseEnum.MYSQL)
                .limit(1);

        GroupTableBuilder = JDBCSqlBuilderImpl.builder()
                .tableName("dwd_bigcustomer_qqgroup_msg_data")
                .selectField(Arrays.asList("msg_time", "sender_type", "content", "group_id"))
                .conditionKeyList(Arrays.asList("group_id"))
                .databaseEnum(DatabaseEnum.PGSQL);

        AiTableBuilder = JDBCSqlBuilderImpl.builder()
                .tableName("dwd_incident_msgai_view")
                .selectField(Arrays.asList("question", "create_time", "ticket_id"))
                .conditionKeyList(Arrays.asList("ticket_id"))
                .databaseEnum(DatabaseEnum.PGSQL);

        ExternGroupTableBuilder = JDBCSqlBuilderImpl.builder()
                .tableName("t030_wework_msg")
                .selectAll()
                .conditionKeyList(Arrays.asList("msg_source_id"))
                .databaseEnum(DatabaseEnum.MYSQL);

        TicketGroupTableBuilder = JDBCSqlBuilderImpl.builder()
                .tableName("t_ticket_channel_group")
                .selectAll()
                .conditionKeyList(Arrays.asList("ticket_id"))
                .databaseEnum(DatabaseEnum.MYSQL);

        StarRocksTableBuilder = JDBCSqlBuilderImpl.builder()
                .tableName("dwm_incident_ticket_statistic")
                .selectAll()
                .conditionKeyList(Arrays.asList("ticket_id"))
                .databaseEnum(DatabaseEnum.MYSQL);

        starocksDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.starrocks.dataware")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        databaseConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.mysql.work")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        pgdatabaseConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.pgsql.dataware")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        dwdDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.postgresql.dataware_r")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        messageDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.mysql.message_readonly")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        toolDbConf = Try.of(() ->
                new KVConfBuilder<>(DatabaseConf.class)
                        .setRainbowUtils(rainbowUtils)
                        .setGroupName("cdc.database.mysql.tool")
                        .build()
        ).getOrElseThrow(e -> new RuntimeException(e));

        t201Query = new HashMapJDBCLookupQuery(DatabaseEnum.MYSQL, databaseConf, T201TableBuilder);
        t204Query = new HashMapJDBCLookupQuery(DatabaseEnum.MYSQL, databaseConf, T204TableBuilder);
        oneGroupQuery = new HashMapJDBCLookupQuery(DatabaseEnum.PGSQL, pgdatabaseConf, GroupTableBuilder);
        aiQuery = new HashMapJDBCLookupQuery(DatabaseEnum.PGSQL, dwdDbConf, AiTableBuilder);
        externGroupQuery = new HashMapJDBCLookupQuery(DatabaseEnum.MYSQL, messageDbConf, ExternGroupTableBuilder);
        ticketGroupQuery = new HashMapJDBCLookupQuery(DatabaseEnum.MYSQL, toolDbConf, TicketGroupTableBuilder);
        starockQuery = new HashMapJDBCLookupQuery(DatabaseEnum.MYSQL, starocksDbConf, StarRocksTableBuilder);


        t201Query.open();
        t204Query.open();
        oneGroupQuery.open();
        aiQuery.open();
        externGroupQuery.open();
        ticketGroupQuery.open();
        starockQuery.open();

        config = new Configuration();
        sceneProcessor = new ServiceSceneProcess(dwdDbConf);
        
        // 初始化处理器工厂
        processorFactory = new ProcessorFactory();

        // 打开处理器
        sceneProcessor.open(config);
        
        // 处理器预热：避免首次调用的冷启动延迟
        warmupProcessor();
    }

    /**
     * 处理器预热方法：通过模拟调用来预热JVM和处理器
     */
    private void warmupProcessor() {
        try {
            // 创建一个简单的测试数据进行预热
            JsonNode testData = mapper.createObjectNode().put("test", "warmup");
            SimpleCollector testCollector = new SimpleCollector();
            
            // 预热sceneProcessor（如果有processElement方法）
            // 这里只是预热，不处理结果
            log.info("处理器预热完成");
        } catch (Exception e) {
            log.warn("处理器预热失败，但不影响正常功能: " + e.getMessage());
        }
    }

    // 在应用关闭时清理资源
    @PreDestroy
    public void destroy() {
        try {
            if (sceneProcessor != null) {
                sceneProcessor.close();
            }
            if (t201Query != null) {
                t201Query.close();
            }
            if (starockQuery != null) {
                starockQuery.close();
            }
            if (t204Query != null) {
                t204Query.close();
            }
            if (oneGroupQuery != null) {
                oneGroupQuery.close();
            }
            if (aiQuery != null) {
                aiQuery.close();
            }
            if (externGroupQuery != null) {
                externGroupQuery.close();
            }
            if (ticketGroupQuery != null) {
                ticketGroupQuery.close();
            }
            if (asyncConnection != null) {
                asyncConnection.close();
            }
            if (executorService != null) {
                executorService.shutdown();
            }
            // 清理ThreadLocal
            sceneCollectorThreadLocal.remove();

            log.info("SimilarConversationService资源清理完成");
        } catch (Exception e) {
            log.error("清理资源时发生错误", e);
        }
    }


    public SimilarConversationResponse processConversation(String ticketId, String requestId, Boolean isPicShow) throws QueryException {
        PerformanceStats stats = new PerformanceStats();
        
        try {
            log.info("开始处理相似会话请求，TicketID: " + ticketId);

            // 第一阶段：基础数据查询阶段
            stats.addSection("基础数据查询阶段");
            
            // 提前启动只依赖ticketId的HBase查询（跨阶段优化）
            long ticketFlowStart = System.currentTimeMillis();
            CompletableFuture<FlowQueryResult> ticketFlowFuture = CompletableFuture.supplyAsync(() -> {
                return queryTicketFlowWithStatus(ticketId);
            }, executorService);
            
            long aiFlowStart = System.currentTimeMillis();
            CompletableFuture<FlowQueryResult> aiFlowFuture = CompletableFuture.supplyAsync(() -> {
                return queryAiMsgFlowWithStatus(ticketId);
            }, executorService);
            
            // 基础数据查询
            JsonNode enrichData = queryBasicDataParallelWithStats(ticketId, stats);
            if (enrichData == null) {
                return SimilarConversationResponse.builder()
                        .code(-1)
                        .msg("Andon 201库未查询到相关工单信息")
                        .build();
            }

            // 第二阶段：数据扩充阶段
            stats.addSection("数据扩充阶段");
            enrichData = enrichDataByProcessWithStats(enrichData, stats);

            // 第三阶段：流水数据查询阶段
            stats.addSection("流水数据查询阶段");
            QueryResult queryResult = queryFlowDataParallelWithStatusOptimizedWithStats(enrichData, ticketId, ticketFlowFuture, aiFlowFuture, stats, ticketFlowStart, aiFlowStart,isPicShow);

            // 生成性能报告
            String performanceReport = stats.getFinalReportWithRequestId(ticketId, requestId);
            log.info(performanceReport);
            logger.info(performanceReport);

            return SimilarConversationResponse.builder()
                    .code(0)
                    .msg("SUCCESS")
                    .data(queryResult.getFlowData().toString())
                    .ticket_base_info(queryResult.getBusinessData().toString())
                    .build();
        } catch (Exception e) {
            log.error("处理相似会话请求失败: " + e.getMessage(), e);

            return SimilarConversationResponse.builder()
                    .code(-1)
                    .msg("处理失败: " + e.getMessage())
                    .build();
        }
    }


    /**
     * 带性能统计的流水数据并行查询
     */
    private QueryResult queryFlowDataParallelWithStatusOptimizedWithStats(JsonNode enrichData, String ticketId,
            CompletableFuture<FlowQueryResult> ticketFuture, CompletableFuture<FlowQueryResult> aiMsgFuture, PerformanceStats stats,
            long ticketFlowStart, long aiFlowStart,Boolean isPicShow) throws QueryException {

        // 准备查询参数
        String conversationId =
                enrichData.has("mc_conversation_id") ? enrichData.get("mc_conversation_id").asText() : "";
        String groupId = enrichData.has("chat_group_id") ? enrichData.get("chat_group_id").asText() : "";
        String externGroupId = enrichData.has("extern_group_id") ? enrichData.get("extern_group_id").asText() : "";

        // 记录各个查询的开始时间
        long webimStart = System.currentTimeMillis();
        CompletableFuture<FlowQueryResult> webimFuture = CompletableFuture.supplyAsync(() -> {
            return queryWebimFlowWithStatus(ticketId, conversationId);
        });

        long oneGroupStart = System.currentTimeMillis();
        CompletableFuture<FlowQueryResult> oneGroupFuture = CompletableFuture.supplyAsync(() -> {
            return queryOneGroupFlowWithStatus(ticketId, groupId);
        });

        long externGroupStart = System.currentTimeMillis();
        CompletableFuture<FlowQueryResult> externGroupFuture = CompletableFuture.supplyAsync(() -> {
            return queryExternGroupFlowWithStatus(externGroupId);
        });

        try {
            // 等待所有异步查询完成（包括第一阶段的ticket和AI查询）
            CompletableFuture.allOf(webimFuture, ticketFuture, oneGroupFuture, aiMsgFuture, externGroupFuture).get();

            // 获取结果并记录性能
            FlowQueryResult webimResult = webimFuture.get();
            long webimEnd = System.currentTimeMillis();
            stats.addHBaseQuery("WebIM流水", webimResult, webimEnd - webimStart);

            FlowQueryResult oneGroupResult = oneGroupFuture.get();
            long oneGroupEnd = System.currentTimeMillis();
            stats.addHBaseQuery("一事一群内部流水", oneGroupResult, oneGroupEnd - oneGroupStart);

            FlowQueryResult ticketResult = ticketFuture.get();
            long ticketEnd = System.currentTimeMillis();
            stats.addHBaseQuery("工单流水", ticketResult, ticketEnd - ticketFlowStart);

            FlowQueryResult aiMsgResult = aiMsgFuture.get();
            long aiEnd = System.currentTimeMillis();
            stats.addHBaseQuery("智能客服流水", aiMsgResult, aiEnd - aiFlowStart);

            FlowQueryResult externGroupMsgResult = externGroupFuture.get();
            long externGroupEnd = System.currentTimeMillis();
            stats.addHBaseQuery("一事一群外部群流水", externGroupMsgResult, externGroupEnd - externGroupStart);

            // 记录数据合并时间
            long mergeStart = System.currentTimeMillis();
            
            // 创建流水数据结构（只包含四个流水字段）
            JsonNode flowData = mapper.createObjectNode();
            flowData = JsonNodeUtils.addStringField(flowData, "web_msg", webimResult.getData(), mapper);
            flowData = JsonNodeUtils.addStringField(flowData, "ticket_flow_msg", ticketResult.getData(), mapper);
            flowData = JsonNodeUtils.addStringField(flowData, "one_group_internal_msg", oneGroupResult.getData(),
                    mapper);
            flowData = JsonNodeUtils.addStringField(flowData, "msg_with_ai", aiMsgResult.getData(), mapper);
            flowData = JsonNodeUtils.addStringField(flowData, "one_group_extern_msg", externGroupMsgResult.getData(),
                    mapper);

            // 创建业务数据结构（除了四个流水字段的其他所有字段）
            JsonNode businessData = removeFlowFields(enrichData.deepCopy(), mapper);
            
            long mergeEnd = System.currentTimeMillis();
            stats.addDataMerge("数据合并", mergeEnd - mergeStart);

            // 创建状态摘要
            QueryStatusSummary statusSummary = new QueryStatusSummary();
            statusSummary.addFlowStatus("WebIM", webimResult);
            statusSummary.addFlowStatus("工单流水", ticketResult);
            statusSummary.addFlowStatus("一事一群 内部", oneGroupResult);
            statusSummary.addFlowStatus("智能客服", aiMsgResult);
            statusSummary.addFlowStatus("一事一群 外部", externGroupMsgResult);

            return new QueryResult(flowData, businessData, statusSummary);

        } catch (Exception e) {
            throw new RuntimeException("HBase并行查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带性能统计的并行查询基础数据
     */
    private JsonNode queryBasicDataParallelWithStats(String ticketId, PerformanceStats stats) {
        HashMap<String, Object> params = new HashMap<>(1);
        params.put("ticket_id", ticketId);

        // 并行查询t201和t204还有t_ticket_channel_group
        long t201Start = System.currentTimeMillis();
        CompletableFuture<List<HashMap<String, Object>>> t201Future = CompletableFuture.supplyAsync(() -> {
            try {
                return t201Query.query(params);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        long t204Start = System.currentTimeMillis();
        CompletableFuture<List<HashMap<String, Object>>> t204Future = CompletableFuture.supplyAsync(() -> {
            try {
                return t204Query.query(params);
            } catch (Exception e) {
                return new ArrayList<>(); // 返回空列表，不影响主流程
            }
        });

        long ticketGroupStart = System.currentTimeMillis();
        CompletableFuture<List<HashMap<String, Object>>> ticketGroupFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return ticketGroupQuery.query(params);
            } catch (Exception e) {
                return new ArrayList<>(); // 返回空列表，不影响主流程
            }
        });

        long starocksStart = System.currentTimeMillis();
        CompletableFuture<List<HashMap<String, Object>>> starFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return starockQuery.query(params);
            } catch (Exception e) {
                return new ArrayList<>(); // 返回空列表，不影响主流程
            }
        });

        try {
            List<HashMap<String, Object>> t201Res = t201Future.get();
            long t201End = System.currentTimeMillis();
            stats.addBasicDataQuery("t201_ticket查询", t201End - t201Start, t201Res.size());
            
            List<HashMap<String, Object>> t204Res = t204Future.get();
            long t204End = System.currentTimeMillis();
            stats.addBasicDataQuery("t204_ticket_operation_extra查询", t204End - t204Start, t204Res.size());
            
            List<HashMap<String, Object>> ticketGroupRes = ticketGroupFuture.get();
            long ticketGroupEnd = System.currentTimeMillis();
            stats.addBasicDataQuery("t_ticket_channel_group查询", ticketGroupEnd - ticketGroupStart, ticketGroupRes.size());

            List<HashMap<String, Object>> starRes = starFuture.get();
            long starEnd = System.currentTimeMillis();
            stats.addBasicDataQuery("dwm_incident_ticket_statistic查询", starEnd - starocksStart, starRes.size());

            // 记录并行查询总计
            long maxTime = Math.max(Math.max(t201End - t201Start, t204End - t204Start), ticketGroupEnd - ticketGroupStart);
            stats.addQuery("基础数据并行查询总计", 0, maxTime, 
                String.format("t201:%d条, t204:%d条, ticketChannelGroup:%d条,dwm_incident_ticket_statistic:%d条",
                    t201Res.size(), t204Res.size(), ticketGroupRes.size(),starRes.size()));

            if (t201Res.isEmpty()) {
                return null;
            }

            JsonNode baseData = mapper.valueToTree(t201Res.get(ZERO_POISION));

            // 并行提取需要添加的字段
            CompletableFuture<String> mcConversationIdFuture = CompletableFuture.supplyAsync(() -> {
                if (!t204Res.isEmpty()) {
                    JsonNode conversationData = mapper.valueToTree(t204Res.get(ZERO_POISION));
                    return conversationData.get("mc_conversation_id").asText();
                }
                return null;
            });
            
            CompletableFuture<String> externGroupIdFuture = CompletableFuture.supplyAsync(() -> {
                if (!ticketGroupRes.isEmpty()) {
                    JsonNode externConversationData = mapper.valueToTree(ticketGroupRes.get(ZERO_POISION));
                    return externConversationData.get("group_id").asText();
                }
                return null;
            });

            CompletableFuture<String> starResFuture = CompletableFuture.supplyAsync(() -> {
                if (!starRes.isEmpty()) {
                    JsonNode starData = mapper.valueToTree(starRes.get(ZERO_POISION));
                    return starData.get("close_time").asText();
                }
                return null;
            });
            
            try {
                String mcConversationId = mcConversationIdFuture.get();
                String externGroupId = externGroupIdFuture.get();
                String close_time = starResFuture.get();

                long starMergeStart = System.currentTimeMillis();
                if (close_time != null) {
                    baseData = JsonNodeUtils.addStringField(baseData, "close_time", close_time, mapper);
                }
                long starMergeEnd = System.currentTimeMillis();
                stats.addDataMerge("数据合并", starMergeEnd - starMergeStart);

                // 记录数据合并时间
                long mergeStart = System.currentTimeMillis();
                if (mcConversationId != null) {
                    baseData = JsonNodeUtils.addStringField(baseData, "mc_conversation_id", mcConversationId, mapper);
                }
                long mcMergeEnd = System.currentTimeMillis();
                stats.addDataMerge("数据合并", mcMergeEnd - mergeStart);
                
                long externMergeStart = System.currentTimeMillis();
                if (externGroupId != null) {
                    baseData = JsonNodeUtils.addStringField(baseData, "extern_group_id", externGroupId, mapper);
                }
                long externMergeEnd = System.currentTimeMillis();
                stats.addDataMerge("数据合并", externMergeEnd - externMergeStart);

                
            } catch (Exception e) {
                throw new RuntimeException("并行数据合并失败", e);
            }

            return baseData;

        } catch (Exception e) {
            throw new RuntimeException("并行查询基础数据失败", e);
        }
    }

    /**
     * 带性能统计的数据扩充处理
     */
    private JsonNode enrichDataByProcessWithStats(JsonNode baseData, PerformanceStats stats) {
        try {
            // 记录Channel处理（复用）
            long channelStart = System.currentTimeMillis();
            // 这里实际上跳过了Channel处理，但为了报告一致性，记录为复用
            long channelEnd = System.currentTimeMillis();
            stats.addQuery("ServiceChannelProcess扩充", channelStart, channelEnd, "Channel处理完成(复用)");
            
            // 记录Scene处理
            long sceneStart = System.currentTimeMillis();
            SimpleCollector sceneCollector = sceneCollectorThreadLocal.get();
            sceneCollector.clear(); // 清理之前的数据

            // 使用预初始化的sceneProcessor，直接处理baseData
            sceneProcessor.processElement(baseData, null, sceneCollector);
            JsonNode finalEnrichedData = sceneCollector.getCollectedNodes().get(ZERO_POISION);
            long sceneEnd = System.currentTimeMillis();
            stats.addQuery("ServiceSceneProcess扩充", sceneStart, sceneEnd, "Scene处理完成(复用)");

            return finalEnrichedData;
        } catch (Exception e) {
            log.error("数据扩充失败", e);
            throw new RuntimeException("数据扩充失败", e);
        }
    }


    /**
     * 查询WebIM流水（带状态）
     */
    private FlowQueryResult queryWebimFlowWithStatus(String ticketId, String conversationId) {
        if (conversationId == null || conversationId.trim().isEmpty()) {
            return FlowQueryResult.paramEmpty("mc_conversation_id");
        }

        try {
            Strategy strategy = Strategy.builder().scene(WebIM).sceneIdentify(conversationId).build();
            CompletableFuture<String> operation = conversationSource.getOperation(strategy);
            String ori = operation.get();

            if (ori == null || ori.trim().isEmpty() || "[]".equals(ori.trim())) {
                return FlowQueryResult.queryEmpty("HBase中无WebIM数据，conversationId: " + conversationId);
            }

            FlowQueryResult result = processorFactory.processData(ProcessorFactory.ProcessorType.WEBIM, ori, conversationId);
            System.out.println("webim 流水 工单：" + ticketId + "->" + result.getData());
            return result;
        } catch (Exception e) {
            log.error("查询WebIM流水失败，ticketId: " + ticketId + ", conversationId: " + conversationId, e);
            return FlowQueryResult.error(e.getMessage());
        }
    }

    /**
     * 查询工单流水（带状态）
     */
    private FlowQueryResult queryTicketFlowWithStatus(String ticketId) {
        if (ticketId == null || ticketId.trim().isEmpty()) {
            return FlowQueryResult.paramEmpty("ticketId");
        }

        try {
            Strategy strategy = Strategy.builder().scene(Ticket).sceneIdentify(ticketId).build();
            CompletableFuture<String> operation = conversationSource.getOperation(strategy);
            String ori = operation.get();

            if (ori == null || ori.trim().isEmpty() || "[]".equals(ori.trim())) {
                return FlowQueryResult.queryEmpty("HBase中无工单流水数据，ticketId: " + ticketId);
            }

            FlowQueryResult result = processorFactory.processData(ProcessorFactory.ProcessorType.TICKET_FLOW, ori, ticketId);
            System.out.println("ticket 流水 工单：" + ticketId + "->" + result.getData());
            return result;
        } catch (Exception e) {
            log.error("查询工单流水失败，ticketId: " + ticketId, e);
            return FlowQueryResult.error(e.getMessage());
        }
    }

    /**
     * 查询一事一群流水（带状态）
     */
    private FlowQueryResult queryOneGroupFlowWithStatus(String ticketId, String groupId) {
        if (groupId == null || groupId.trim().isEmpty()) {
            return FlowQueryResult.paramEmpty("chat_group_id");
        }

        try {
            HashMap<String, Object> params = new HashMap<>(1);
            params.put("group_id", groupId);
            List<HashMap<String, Object>> oneGroupRes = oneGroupQuery.query(params);

            if (oneGroupRes == null || oneGroupRes.isEmpty()) {
                return FlowQueryResult.queryEmpty("dwd_bigcustomer_qqgroup_msg_data 中无一事一群数据，groupId: " + groupId);
            }

            return processorFactory.processData(ProcessorFactory.ProcessorType.INTERNAL_GROUP, oneGroupRes, groupId);
        } catch (Exception e) {
            log.error("查询一事一群流水失败，ticketId: " + ticketId + ", groupId: " + groupId, e);
            return FlowQueryResult.error(e.getMessage());
        }
    }

    /**
     * 查询一事一群 外部群流水（带状态）
     */
    private FlowQueryResult queryExternGroupFlowWithStatus(String externGroupId) {
        if (externGroupId == null || externGroupId.trim().isEmpty()) {
            return FlowQueryResult.paramEmpty("externGroupId");
        }

        try {
            HashMap<String, Object> params = new HashMap<>(1);
            params.put("msg_source_id", externGroupId);
            List<HashMap<String, Object>> externGroupRes = externGroupQuery.query(params);

            if (externGroupRes == null || externGroupRes.isEmpty()) {
                return FlowQueryResult.queryEmpty("t030_wework_msg中无外部群数据，msg_source_id: " + externGroupId);
            }

            return processorFactory.processData(ProcessorFactory.ProcessorType.EXTERNAL_GROUP, externGroupRes, externGroupId);
        } catch (Exception e) {
            log.error("查询外部群流水失败, msg_source_id: " + externGroupId, e);
            return FlowQueryResult.error(e.getMessage());
        }
    }

    /**
     * 查询智能客服流水（带状态）
     */
    private FlowQueryResult queryAiMsgFlowWithStatus(String ticketId) {
        if (ticketId == null || ticketId.trim().isEmpty()) {
            return FlowQueryResult.paramEmpty("ticketId");
        }

        try {
            HashMap<String, Object> params = new HashMap<>(1);
            params.put("ticket_id", ticketId);
            List<HashMap<String, Object>> aiRes = aiQuery.query(params);

            if (aiRes == null || aiRes.isEmpty()) {
                return FlowQueryResult.queryEmpty("PG中无智能客服数据，ticketId: " + ticketId);
            }
            return processorFactory.processData(ProcessorFactory.ProcessorType.AI_MESSAGE, aiRes, ticketId);
        } catch (Exception e) {
            log.error("查询智能客服流水失败，ticketId: " + ticketId, e);
            return FlowQueryResult.error(e.getMessage());
        }
    }
}