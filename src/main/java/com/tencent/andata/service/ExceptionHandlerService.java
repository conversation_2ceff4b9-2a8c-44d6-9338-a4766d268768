package com.tencent.andata.service;

import com.tencent.andata.bean.ConversationResponse;
import com.tencent.andata.constant.ConversationConstants;
import com.tencent.andata.exception.ConversationException;

import io.vavr.Predicates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;

/**
 * 异常处理服务
 * 使用Vavr函数式编程处理会话过程中的各种异常
 */
@Slf4j
@Service
public class ExceptionHandlerService {

    /**
     * 处理会话过程中的异常
     *
     * @param throwable 捕获的异常
     * @return 包含错误信息的响应实体
     */
    public ResponseEntity<ConversationResponse> handleException(Throwable throwable) {
        return ResponseEntity.ok(
            Match(throwable).of(
                Case($(Predicates.instanceOf(InterruptedException.class)), this::handleInterruptedException),
                Case($(Predicates.instanceOf(ExecutionException.class)), this::handleExecutionException),
                Case($(Predicates.instanceOf(TimeoutException.class)), this::handleTimeoutException),
                Case($(Predicates.instanceOf(ConversationException.class)), this::handleConversationException),
                Case($(), this::handleGenericException)
            )
        );
    }

    /**
     * 处理中断异常
     */
    private ConversationResponse handleInterruptedException(InterruptedException e) {
        Thread.currentThread().interrupt(); // 重置中断标志
        return ConversationResponse.error(500,
                String.format(ConversationConstants.ERROR_REQUEST_INTERRUPTED, e.getMessage()));
    }

    /**
     * 处理执行异常
     */
    private ConversationResponse handleExecutionException(ExecutionException e) {
        return ConversationResponse.error(500,
                String.format(ConversationConstants.ERROR_PROCESS_REQUEST, e.getCause().getMessage()));
    }

    /**
     * 处理超时异常
     */
    private ConversationResponse handleTimeoutException(TimeoutException e) {
        return ConversationResponse.error(408, ConversationConstants.ERROR_REQUEST_TIMEOUT);
    }

    /**
     * 处理会话异常
     */
    private ConversationResponse handleConversationException(ConversationException e) {
        log.error("会话处理异常: {}", e.getMessage(), e);
        return ConversationResponse.error(e.getStatusCode(), e.getMessage());
    }

    /**
     * 处理通用异常
     */
    private ConversationResponse handleGenericException(Throwable e) {
        return ConversationResponse.error(500,
                String.format(ConversationConstants.ERROR_PROCESS_REQUEST, e.getMessage()));
    }
}