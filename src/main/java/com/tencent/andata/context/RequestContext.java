package com.tencent.andata.context;

/**
 * 请求上下文管理器
 * 使用ThreadLocal存储request_id，确保在整个请求处理过程中都能访问到request_id
 * 线程安全且自动清理，避免内存泄漏
 */
public class RequestContext {
    
    private static final ThreadLocal<String> REQUEST_ID_HOLDER = new ThreadLocal<>();
    
    /**
     * 设置当前线程的request_id
     * @param requestId 请求ID
     */
    public static void setRequestId(String requestId) {
        REQUEST_ID_HOLDER.set(requestId);
    }
    
    /**
     * 获取当前线程的request_id
     * @return 请求ID，如果未设置则返回null
     */
    public static String getRequestId() {
        return REQUEST_ID_HOLDER.get();
    }
    
    /**
     * 清理当前线程的request_id
     * 防止内存泄漏，在请求处理完成后必须调用
     */
    public static void clear() {
        REQUEST_ID_HOLDER.remove();
    }
    
    /**
     * 检查当前线程是否设置了request_id
     * @return true如果已设置，false如果未设置
     */
    public static boolean hasRequestId() {
        return REQUEST_ID_HOLDER.get() != null;
    }
    
    /**
     * 获取request_id，如果未设置则返回默认值
     * @param defaultValue 默认值
     * @return 请求ID或默认值
     */
    public static String getRequestIdOrDefault(String defaultValue) {
        String requestId = REQUEST_ID_HOLDER.get();
        return requestId != null ? requestId : defaultValue;
    }
}