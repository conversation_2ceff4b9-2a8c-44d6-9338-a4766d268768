package com.tencent.andata.constant;

public class ConversationConstants {
    // Channel types
    public static final String CHANNEL_TICKET = "ticket";
    public static final String CHANNEL_WEBIM = "webim";
    public static final String CHANNEL_GROUP = "group";
    
    // Error messages
    public static final String ERROR_EMPTY_RESULT = "处理结果为空";
    public static final String ERROR_GET_CONVERSATION = "获取会话内容失败";
    public static final String ERROR_INVALID_CHANNEL = "不支持的channel类型: %s";
    public static final String ERROR_PROCESS_RESULT = "处理结果异常: %s";
    public static final String ERROR_PROCESS_REQUEST = "处理请求失败: %s";
    public static final String ERROR_REQUEST_TIMEOUT = "请求处理超时";
    public static final String ERROR_REQUEST_INTERRUPTED = "请求处理被中断: %s";
    
    // Log messages
    public static final String LOG_INIT_SUCCESS = "StrategyChunkBuildConversationAsyncProcess初始化成功";
    public static final String LOG_INIT_FAILED = "初始化ConversationService失败";
    public static final String LOG_CLOSE_SUCCESS = "StrategyChunkBuildConversationAsyncProcess关闭成功";
    public static final String LOG_CLOSE_FAILED = "关闭资源失败: %s";
    public static final String LOG_GET_CONVERSATION_SUCCESS = "获取会话成功，场景类型：%s，ID：%s，会话内容：%s";
    public static final String LOG_GET_CONVERSATION_FAILED = "获取会话内容失败，场景类型：%s，ID：%s";
    
    private ConversationConstants() {
        // 防止实例化
    }
} 