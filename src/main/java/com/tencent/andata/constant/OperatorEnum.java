package com.tencent.andata.constant;

import java.util.HashMap;
import java.util.Map;

public enum OperatorEnum {
    // 客户
    CUSTOMER(1, "客户"),

    // 客服
    STAFF(2, "客服"),

    // 系统
    SYSTEM(3, "系统"),

    // 星云系统
    XINGYUN_SYSTEM(4, "星云"),

    // 故障通告
    FAULT_NOTIFY(5, "故障"),

    // 事件经理
    INCIDENT_MANAGER(6, "事件经理"),

    // 工具
    TOOL(7, "工具");

    private final int code;
    private final String name;

    private static final Map<Integer, OperatorEnum> CODE_MAP = new HashMap<>();

    static {
        for (OperatorEnum type : OperatorEnum.values()) {
            CODE_MAP.put(type.code, type);
        }
    }

    OperatorEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static OperatorEnum fromCode(int code) {
        return CODE_MAP.get(code);
    }

    public static String getNameByCode(int code) {
        OperatorEnum type = CODE_MAP.get(code);
        return type != null ? type.getName() : null;
    }
}