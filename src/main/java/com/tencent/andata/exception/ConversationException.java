package com.tencent.andata.exception;

/**
 * 会话处理异常
 * 表示在处理会话过程中发生的异常
 */
public class ConversationException extends RuntimeException {

    private final int statusCode;

    public ConversationException(String message, int statusCode) {
        super(message);
        this.statusCode = statusCode;
    }

    public ConversationException(String message, Throwable cause, int statusCode) {
        super(message, cause);
        this.statusCode = statusCode;
    }

    public int getStatusCode() {
        return statusCode;
    }
}