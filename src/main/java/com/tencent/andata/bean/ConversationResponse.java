package com.tencent.andata.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationResponse {
    private int code;  // 响应码，200表示成功
    private String data;  // 会话内容
    private String errInfo;  // 错误信息
    
    public static ConversationResponse success(String data) {
        return ConversationResponse.builder()
                .code(200)
                .data(data)
                .errInfo(null)
                .build();
    }
    
    public static ConversationResponse error(int code, String errInfo) {
        return ConversationResponse.builder()
                .code(code)
                .data(null)
                .errInfo(errInfo)
                .build();
    }
}