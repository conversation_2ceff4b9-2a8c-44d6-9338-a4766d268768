package com.tencent.andata.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimilarConversationResponse {

    private int code;  // 响应码，0表示成功
    private String data;  // 流水会话内容
    private String ticket_base_info;  // 基础字段
    private String msg;  // 错误信息

    public static SimilarConversationResponse success(String data) {
        return SimilarConversationResponse.builder()
                .code(200)
                .data(data)
                .msg(null)
                .ticket_base_info(null)
                .build();
    }

    public static SimilarConversationResponse error(int code, String errInfo) {
        return SimilarConversationResponse.builder()
                .code(code)
                .data(null)
                .ticket_base_info(null)
                .msg(errInfo)
                .build();
    }
}