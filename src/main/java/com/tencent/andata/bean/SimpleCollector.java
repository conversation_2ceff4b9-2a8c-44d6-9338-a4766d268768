package com.tencent.andata.bean;

import java.util.ArrayList;
import java.util.List;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.util.Collector;

/**
 * 简单的JsonNode收集器
 */
public class SimpleCollector implements Collector<JsonNode> {

    private final List<JsonNode> collectedNodes = new ArrayList<>();

    @Override
    public void collect(JsonNode node) {
        collectedNodes.add(node);
    }

    @Override
    public void close() {
        // 不需要实现
    }

    public void clear() {
        // 不需要实现
        collectedNodes.clear();
    }

    public List<JsonNode> getCollectedNodes() {
        return collectedNodes;
    }
}