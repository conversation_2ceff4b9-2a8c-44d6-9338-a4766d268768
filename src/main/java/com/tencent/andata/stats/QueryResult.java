package com.tencent.andata.stats;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

// 查询结果包装类
public class QueryResult {
    private JsonNode flowData;     // 只包含四个流水字段
    private JsonNode businessData; // 包含其他业务字段
    private QueryStatusSummary statusSummary;

    public QueryResult(JsonNode flowData, JsonNode businessData, QueryStatusSummary statusSummary) {
        this.flowData = flowData;
        this.businessData = businessData;
        this.statusSummary = statusSummary;
    }

    public JsonNode getFlowData() {
        return flowData;
    }

    public JsonNode getBusinessData() {
        return businessData;
    }

    public String getStatusMessage(long totalTimeMs) {
        return statusSummary.getSummaryMessage(totalTimeMs);
    }
}