package com.tencent.andata.stats;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 性能统计类 - 增强版，支持详细的分阶段性能报告
public class PerformanceStats {

    private StringBuilder stats = new StringBuilder();
    private long totalStartTime;
    private String currentSection = "";
    
    // 存储各阶段的详细信息
    private Map<String, List<QueryInfo>> sectionQueries = new HashMap<>();
    private Map<String, Long> sectionStartTimes = new HashMap<>();
    private Map<String, Long> sectionDurations = new HashMap<>();
    
    // 查询信息内部类
    private static class QueryInfo {
        String name;
        long duration;
        String resultInfo;
        int resultCount;
        
        QueryInfo(String name, long duration, String resultInfo, int resultCount) {
            this.name = name;
            this.duration = duration;
            this.resultInfo = resultInfo;
            this.resultCount = resultCount;
        }
    }

    public PerformanceStats() {
        this.totalStartTime = System.currentTimeMillis();
    }

    public void addQuery(String queryName, long startTime, long endTime, int resultCount) {
        long duration = endTime - startTime;
        String resultInfo = "结果数量: " + resultCount;
        addQueryToCurrentSection(queryName, duration, resultInfo, resultCount);
    }

    public void addQuery(String queryName, long startTime, long endTime, String resultInfo) {
        long duration = endTime - startTime;
        addQueryToCurrentSection(queryName, duration, resultInfo, -1);
    }
    
    private void addQueryToCurrentSection(String queryName, long duration, String resultInfo, int resultCount) {
        if (!currentSection.isEmpty()) {
            sectionQueries.computeIfAbsent(currentSection, k -> new ArrayList<>())
                .add(new QueryInfo(queryName, duration, resultInfo, resultCount));
        }
    }

    public void addSection(String sectionName) {
        // 结束上一个阶段的计时
        if (!currentSection.isEmpty() && sectionStartTimes.containsKey(currentSection)) {
            long sectionDuration = System.currentTimeMillis() - sectionStartTimes.get(currentSection);
            sectionDurations.put(currentSection, sectionDuration);
        }
        
        // 开始新阶段
        currentSection = sectionName;
        sectionStartTimes.put(sectionName, System.currentTimeMillis());
        sectionQueries.computeIfAbsent(sectionName, k -> new ArrayList<>());
    }
    
    // 添加基础数据查询的详细信息
    public void addBasicDataQuery(String tableName, long duration, int resultCount) {
        if ("基础数据查询阶段".equals(currentSection)) {
            String queryName = tableName + "查询";
            String resultInfo = "结果数量: " + resultCount;
            addQueryToCurrentSection(queryName, duration, resultInfo, resultCount);
        }
    }
    
    // 添加数据合并操作
    public void addDataMerge(String operation, long duration) {
        if (!currentSection.isEmpty()) {
            addQueryToCurrentSection("数据合并", duration, operation, -1);
        }
    }
    
    // 添加HBase查询结果
    public void addHBaseQuery(String queryType, FlowQueryResult result, long duration) {
        if (!currentSection.isEmpty()) {
            String resultInfo;
            switch (result.getStatus()) {
                case "SUCCESS":
                    resultInfo = "查询成功，数据长度: " + result.getData().length();
                    break;
                case "PARAM_EMPTY":
                    resultInfo = result.getReason();
                    break;
                case "QUERY_EMPTY":
                    resultInfo = result.getReason();
                    break;
                case "ERROR":
                    resultInfo = "查询异常: " + result.getReason();
                    break;
                default:
                    resultInfo = result.getReason();
            }
            addQueryToCurrentSection(queryType + "查询", duration, resultInfo, -1);
        }
    }

    public String getFinalReport(String ticketId) {
        return getFinalReportWithRequestId(ticketId, null);
    }
    
    public String getFinalReportWithRequestId(String ticketId, String requestId) {
        // 结束最后一个阶段的计时
        if (!currentSection.isEmpty() && sectionStartTimes.containsKey(currentSection)) {
            long sectionDuration = System.currentTimeMillis() - sectionStartTimes.get(currentSection);
            sectionDurations.put(currentSection, sectionDuration);
        }
        
        StringBuilder report = new StringBuilder();
        
        // 添加requestId（如果有）
        if (requestId != null && !requestId.trim().isEmpty()) {
            report.append("requestId：").append(requestId).append("\n");
        }
        
        report.append("TicketID: ").append(ticketId).append("\n");
        report.append("=== 相似会话查询性能统计报告 ===\n\n");
        
        // 生成各阶段的详细报告
        generateBasicDataSection(report);
        generateDataEnrichmentSection(report);
        generateFlowDataSection(report);
        
        // 总处理耗时
        long totalEndTime = System.currentTimeMillis();
        long totalDuration = totalEndTime - totalStartTime;
        report.append("\n总处理耗时: ").append(totalDuration).append(" ms\n");
        report.append("=== 性能统计报告结束 ===\n");
        
        return report.toString();
    }
    
    private void generateBasicDataSection(StringBuilder report) {
        String sectionName = "基础数据查询阶段";
        List<QueryInfo> queries = sectionQueries.get(sectionName);
        if (queries != null && !queries.isEmpty()) {
            report.append("--- ").append(sectionName).append(" ---\n");
            
            // 统计各表查询信息
            Map<String, QueryInfo> tableQueries = new HashMap<>();
            for (QueryInfo query : queries) {
                if (query.name.contains("查询")) {
                    tableQueries.put(query.name, query);
                }
            }
            
            // 输出各表查询详情
            for (QueryInfo query : tableQueries.values()) {
                report.append(String.format("%-30s: %4d ms (%s)\n", 
                    query.name, query.duration, query.resultInfo));
            }
            
            // 计算并行查询总计
            if (!tableQueries.isEmpty()) {
                long maxDuration = tableQueries.values().stream()
                    .mapToLong(q -> q.duration).max().orElse(0);
                
                StringBuilder summary = new StringBuilder();
                tableQueries.forEach((name, query) -> {
                    String tableName = name.replace("查询", "");
                    if (summary.length() > 0) summary.append(", ");
                    summary.append(tableName).append(":").append(query.resultCount).append("条");
                });
                
                report.append(String.format("%-30s: %4d ms (%s)\n", 
                    "基础数据并行查询总计", maxDuration, summary.toString()));
            }
            
            // 输出数据合并操作
            for (QueryInfo query : queries) {
                if ("数据合并".equals(query.name)) {
                    report.append(String.format("%-30s: %4d ms (%s)\n", 
                        query.name, query.duration, query.resultInfo));
                }
            }
            
            report.append("\n");
        }
    }
    
    private void generateDataEnrichmentSection(StringBuilder report) {
        String sectionName = "数据扩充阶段";
        List<QueryInfo> queries = sectionQueries.get(sectionName);
        if (queries != null && !queries.isEmpty()) {
            report.append("--- ").append(sectionName).append(" ---\n");
            
            long totalDuration = 0;
            StringBuilder summary = new StringBuilder();
            
            for (QueryInfo query : queries) {
                report.append(String.format("%-30s: %4d ms (%s)\n", 
                    query.name, query.duration, query.resultInfo));
                totalDuration += query.duration;
                
                if (summary.length() > 0) summary.append(", ");
                String processName = query.name.replace("扩充", "").replace("Process", "");
                summary.append(processName).append(":").append(query.duration).append("ms");
            }
            
            if (totalDuration > 0) {
                report.append(String.format("%-30s: %4d ms (%s)\n", 
                    "数据扩充总计", totalDuration, summary.toString() + " (优化后)"));
            }
            
            report.append("\n");
        }
    }
    
    private void generateFlowDataSection(StringBuilder report) {
        String sectionName = "流水数据查询阶段";
        List<QueryInfo> queries = sectionQueries.get(sectionName);
        if (queries != null && !queries.isEmpty()) {
            report.append("--- ").append(sectionName).append(" ---\n");
            
            // 输出各流水查询详情
            Map<String, String> statusMap = new HashMap<>();
            for (QueryInfo query : queries) {
                if (query.name.contains("查询")) {
                    report.append(String.format("%-30s: %4d ms (%s)\n", 
                        query.name, query.duration, query.resultInfo));
                    
                    // 收集状态信息用于HBase并行查询总计
                    String status = getQueryStatus(query.resultInfo);
                    String queryType = query.name.replace("查询", "");
                    statusMap.put(queryType, status);
                }
            }
            
            // 输出数据合并操作
            for (QueryInfo query : queries) {
                if ("数据合并".equals(query.name)) {
                    report.append(String.format("%-30s: %4d ms (%s)\n", 
                        query.name, query.duration, query.resultInfo));
                }
            }
            
            // 计算HBase并行查询总计
            if (!statusMap.isEmpty()) {
                long maxDuration = queries.stream()
                    .filter(q -> q.name.contains("查询"))
                    .mapToLong(q -> q.duration).max().orElse(0);
                
                StringBuilder summary = new StringBuilder();
                statusMap.forEach((type, status) -> {
                    if (summary.length() > 0) summary.append(", ");
                    summary.append(type).append(":").append(status);
                });
                
                report.append(String.format("%-30s: %4d ms (%s)\n", 
                    "HBase并行查询总计", maxDuration, summary.toString()));
            }
            
            report.append("\n");
        }
    }
    
    private String getQueryStatus(String resultInfo) {
        if (resultInfo.contains("参数为空")) {
            return "PARAM_EMPTY";
        } else if (resultInfo.contains("查询结果为空")) {
            return "QUERY_EMPTY";
        } else if (resultInfo.contains("查询成功")) {
            return "SUCCESS";
        } else if (resultInfo.contains("查询异常")) {
            return "ERROR";
        } else {
            return "UNKNOWN";
        }
    }
}