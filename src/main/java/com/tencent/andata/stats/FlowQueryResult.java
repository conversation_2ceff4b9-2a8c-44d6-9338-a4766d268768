package com.tencent.andata.stats;

public class FlowQueryResult {

    private String data;
    private String status;
    private String reason;

    public FlowQueryResult(String data, String status, String reason) {
        this.data = data;
        this.status = status;
        this.reason = reason;
    }

    public static FlowQueryResult success(String data) {
        return new FlowQueryResult(data, "SUCCESS", "查询成功，数据长度: " + data.length());
    }

    public static FlowQueryResult paramEmpty(String paramName) {
        return new FlowQueryResult("", "PARAM_EMPTY", "参数为空: " + paramName);
    }

    public static FlowQueryResult queryEmpty(String queryInfo) {
        return new FlowQueryResult("", "QUERY_EMPTY", "查询结果为空: " + queryInfo);
    }

    public static FlowQueryResult error(String errorMsg) {
        return new FlowQueryResult("", "ERROR", "查询异常: " + errorMsg);
    }

    public String getData() {
        return data;
    }

    public String getStatus() {
        return status;
    }

    public String getReason() {
        return reason;
    }
}