package com.tencent.andata.stats;

// 查询状态摘要类
public class QueryStatusSummary {

    private StringBuilder summary = new StringBuilder();

    public void addFlowStatus(String flowName, FlowQueryResult result) {
        String statusIcon = getStatusIcon(result.getStatus());
        summary.append(String.format("%s %s: %s; ", statusIcon, flowName, result.getReason()));
    }

    private String getStatusIcon(String status) {
        switch (status) {
            case "SUCCESS":
                return "✓";
            case "PARAM_EMPTY":
                return "⚠";
            case "QUERY_EMPTY":
                return "○";
            case "ERROR":
                return "✗";
            default:
                return "?";
        }
    }

    public String getSummaryMessage(long totalTimeMs) {
        return String.format("处理完成(耗时:%dms) - %s", totalTimeMs, summary.toString().trim());
    }
}