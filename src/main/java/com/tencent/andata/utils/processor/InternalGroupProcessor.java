package com.tencent.andata.utils.processor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.stats.FlowQueryResult;
import com.tencent.andata.utils.HtmlToTextUtils;
import com.tencent.andata.utils.JsonNodeUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 一事一群内部群消息处理器
 * 负责处理内部群的消息查询结果，对应原MessageProcessor中的processOneGroupMessages逻辑
 *
 * <AUTHOR>
 */
@Slf4j
public class InternalGroupProcessor implements DataProcessor {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 图片消息匹配模式：匹配只包含图片的消息
    private static final Pattern IMAGE_ONLY_PATTERN = Pattern.compile("^\\s*(?:\\[图片\\]\\([^)]*\\)\\s*)+$");

    @Override
    public String getProcessorType() {
        return "InternalGroup";
    }

    /**
     * 处理群消息查询结果，返回按group_id分组的消息字符串
     *
     * @param messageRecords 群消息查询结果列表
     * @return 按group_id分组的格式化消息字符串映射
     */
    private Map<String, String> processGroupMessages(List<HashMap<String, Object>> messageRecords) {
        if (messageRecords == null || messageRecords.isEmpty()) {
            log.debug("内部群消息记录为空");
            return new HashMap<>();
        }

        log.info("开始处理内部群消息，记录数: {}", messageRecords.size());

        return messageRecords.stream()
                .map(this::processInternalGroupSingleMessage)
                .filter(message -> !isUnsupportedMessage(message.getFormattedMessage()))
                .filter(message -> message.getFormattedMessage().trim().length() > 0)
                .filter(message -> !isImageOnlyMessage(message.getFormattedMessage()))
                .collect(Collectors.groupingBy(
                        ProcessedMessage::getGroupId,
                        Collectors.mapping(
                                ProcessedMessage::getFormattedMessage,
                                Collectors.joining("")
                        )
                ));
    }

    @Override
    public FlowQueryResult processData(Object data, String identifier) {
        if (!validateInput(data)) {
            return handleEmptyData(identifier);
        }

        try {
            @SuppressWarnings("unchecked")
            List<HashMap<String, Object>> groupData = (List<HashMap<String, Object>>) data;
            Map<String, String> result = processGroupMessages(groupData);
            String message = result.get(identifier);

            if (message == null || message.trim().isEmpty()) {
                return FlowQueryResult.queryEmpty("一事一群处理后数据为空，groupId: " + identifier);
            }

            return FlowQueryResult.success(message);
        } catch (Exception e) {
            return handleException(e, identifier);
        }
    }

    @Override
    public boolean validateInput(Object data) {
        return data != null && data instanceof List && !((List<?>) data).isEmpty();
    }

    @Override
    public FlowQueryResult handleEmptyData(String identifier) {
        return FlowQueryResult.queryEmpty("一事一群处理后数据为空，groupId: " + identifier);
    }

    @Override
    public FlowQueryResult handleException(Exception e, String identifier) {
        log.error("处理内部群消息时发生异常，groupId: {}", identifier, e);
        return FlowQueryResult.error("处理内部群消息时发生异常: " + e.getMessage());
    }

    /**
     * 处理单条内部群消息记录
     * 对应原MessageProcessor中的processInternalGroupSingleMessage逻辑
     */
    private ProcessedMessage processInternalGroupSingleMessage(HashMap<String, Object> record) {
        String senderType = String.valueOf(record.get("sender_type"));
        String msgTime = JsonNodeUtils.formatDateTimeToStandard(String.valueOf(record.get("msg_time")));
        String content = String.valueOf(record.get("content"));
        String groupId = String.valueOf(record.get("group_id"));

        // 解析内部群消息内容
        String parsedContent = parseInternalGroupMsgContent(content);
        
        // 使用HtmlToTextUtils清理HTML标签
        parsedContent = HtmlToTextUtils.htmlToText(parsedContent);

        // 格式化消息
        String formattedMessage = String.format("[%s] %s\n%s\n\n", senderType, msgTime, parsedContent);

        return new ProcessedMessage(groupId, formattedMessage);
    }

    /**
     * 判断是否为不支持的消息类型
     */
    public boolean isUnsupportedMessage(String message) {
        if (message == null || message.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含 [不支持的消息类型]
        return message.contains("[不支持的消息类型]");
    }
    
    /**
     * 判断是否为只包含图片的消息
     */
    public boolean isImageOnlyMessage(String message) {
        if (message == null || message.trim().isEmpty()) {
            return false;
        }
        
        // 提取消息内容部分（去掉时间戳和发送者信息）
        String[] lines = message.split("\n");
        if (lines.length < 2) {
            return false;
        }
        
        // 获取消息内容部分（第二行开始）
        StringBuilder contentBuilder = new StringBuilder();
        for (int i = 1; i < lines.length; i++) {
            contentBuilder.append(lines[i]).append("\n");
        }
        String content = contentBuilder.toString().trim();
        
        // 检查是否只包含图片链接或不支持的消息类型
        return IMAGE_ONLY_PATTERN.matcher(content).matches() || 
               content.equals("[不支持的消息类型]") ||
               content.matches("^\\s*\\[不支持的消息类型\\]\\s*$");
    }

    /**
     * 解析内部群消息内容
     * 对应原MessageProcessor中的parseInternalGroupMsgContent函数
     * 内部群的content字段可能包含JSON格式的消息列表
     */
    private String parseInternalGroupMsgContent(String content) {
        if (content != null && content.startsWith("[{\"msg_type\":")) {
            try {
                List<Map<String, Object>> msgList = objectMapper.readValue(
                        content, new TypeReference<List<Map<String, Object>>>() {
                        }
                );

                StringBuilder result = new StringBuilder();
                for (Map<String, Object> msg : msgList) {
                    if (msg == null) {
                        continue;
                    }

                    String msgType = String.valueOf(msg.get("msg_type"));
                    switch (msgType) {
                        case "image":
                            String picUrl = String.valueOf(msg.get("pic_url"));
                            result.append(" [图片](").append(picUrl).append(") ");
                            break;
                        case "text":
                            result.append(String.valueOf(msg.get("content")));
                            break;
                        default:
                            // 跳过其他类型
                            break;
                    }
                }
                return result.toString();
            } catch (Exception e) {
                log.warn("解析内部群消息内容失败，使用原始内容: {}", e.getMessage());
                return content;
            }
        }
        return content;
    }

    /**
     * 处理后的消息数据结构
     */
    private static class ProcessedMessage {

        private final String groupId;
        private final String formattedMessage;

        public ProcessedMessage(String groupId, String formattedMessage) {
            this.groupId = groupId;
            this.formattedMessage = formattedMessage;
        }

        public String getGroupId() {
            return groupId;
        }

        public String getFormattedMessage() {
            return formattedMessage;
        }
    }
}