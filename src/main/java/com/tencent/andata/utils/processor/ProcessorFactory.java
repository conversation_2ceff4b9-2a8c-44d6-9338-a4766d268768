package com.tencent.andata.utils.processor;

import com.tencent.andata.stats.FlowQueryResult;
import java.util.HashMap;
import java.util.List;

/**
 * 处理器工厂类
 * 统一管理和调用所有数据处理器
 */
public class ProcessorFactory {
    
    /**
     * 处理器类型枚举
     */
    public enum ProcessorType {
        WEBIM,
        TICKET_FLOW,
        INTERNAL_GROUP,
        EXTERNAL_GROUP,
        AI_MESSAGE
    }
    
    private final WebimDataProcessor webimProcessor;
    private final TicketFlowDataProcessor ticketFlowProcessor;
    private final InternalGroupProcessor internalGroupProcessor;
    private final ExternalGroupProcessor externalGroupProcessor;
    private final AiMessageProcessor aiMessageProcessor;
    
    public ProcessorFactory() {
        this.webimProcessor = new WebimDataProcessor();
        this.ticketFlowProcessor = new TicketFlowDataProcessor();
        this.internalGroupProcessor = new InternalGroupProcessor();
        this.externalGroupProcessor = new ExternalGroupProcessor();
        this.aiMessageProcessor = new AiMessageProcessor();
    }
    
    /**
     * 统一的数据处理方法
     * @param type 处理器类型
     * @param data 要处理的数据
     * @param identifier 标识符（如ticketId、groupId等）
     * @return 处理结果
     */
    public FlowQueryResult processData(ProcessorType type, Object data, String identifier) {
        try {
            switch (type) {
                case WEBIM:
                    return webimProcessor.processData((String) data, identifier);
                    
                case TICKET_FLOW:
                    return ticketFlowProcessor.processData((String) data, identifier);
                    
                case INTERNAL_GROUP:
                    @SuppressWarnings("unchecked")
                    List<HashMap<String, Object>> internalData = (List<HashMap<String, Object>>) data;
                    return internalGroupProcessor.processData(internalData, identifier);
                    
                case EXTERNAL_GROUP:
                    @SuppressWarnings("unchecked")
                    List<HashMap<String, Object>> externalData = (List<HashMap<String, Object>>) data;
                    return externalGroupProcessor.processData(externalData, identifier);
                    
                case AI_MESSAGE:
                    @SuppressWarnings("unchecked")
                    List<HashMap<String, Object>> aiData = (List<HashMap<String, Object>>) data;
                    return aiMessageProcessor.processData(aiData, identifier);
                    
                default:
                    return FlowQueryResult.queryEmpty("未知的处理器类型: " + type);
            }
        } catch (Exception e) {
            return FlowQueryResult.error("处理数据时发生异常: " + e.getMessage());
        }
    }
    
}