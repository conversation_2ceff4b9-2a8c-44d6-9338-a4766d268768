package com.tencent.andata.utils.processor;

import com.tencent.andata.stats.FlowQueryResult;
import com.tencent.andata.utils.WebimProcessor;

/**
 * WebIM数据处理器
 * 适配WebimProcessor到统一的DataProcessor接口
 */
public class WebimDataProcessor implements DataProcessor {
    
    private final WebimProcessor webimProcessor;
    
    public WebimDataProcessor() {
        this.webimProcessor = new WebimProcessor();
    }
    
    @Override
    public FlowQueryResult processData(Object data, String identifier) {
        if (!validateInput(data)) {
            return handleEmptyData(identifier);
        }
        
        try {
            String webimData = (String) data;
            String result = webimProcessor.processWebimData(webimData);
            
            if (result == null || result.trim().isEmpty() || "[]".equals(result.trim())) {
                return FlowQueryResult.queryEmpty("WebIM处理后数据为空，conversationId: " + identifier);
            }
            
            return FlowQueryResult.success(result);
        } catch (Exception e) {
            return handleException(e, identifier);
        }
    }
    
    @Override
    public String getProcessorType() {
        return "WebIM数据处理器";
    }
    
    @Override
    public boolean validateInput(Object data) {
        return data != null && data instanceof String && !((String) data).trim().isEmpty();
    }
    
    @Override
    public FlowQueryResult handleEmptyData(String identifier) {
        return FlowQueryResult.queryEmpty("WebIM处理后数据为空，conversationId: " + identifier);
    }
    
    @Override
    public FlowQueryResult handleException(Exception e, String identifier) {
        return FlowQueryResult.error("WebIM处理异常，conversationId: " + identifier + ", 错误: " + e.getMessage());
    }
}