package com.tencent.andata.utils.processor;

import com.tencent.andata.stats.FlowQueryResult;

/**
 * 数据处理器统一接口
 * 定义所有数据处理器的通用标准
 */
public interface DataProcessor {
    
    /**
     * 处理数据的核心方法
     * @param data 要处理的数据
     * @param identifier 标识符（如ticketId、groupId等）
     * @return 处理后的结果
     */
    FlowQueryResult processData(Object data, String identifier);
    
    /**
     * 获取处理器类型
     * @return 处理器类型标识
     */
    String getProcessorType();
    
    /**
     * 验证输入数据
     * @param data 输入数据
     * @return 是否有效
     */
    public boolean validateInput(Object data);
    
    /**
     * 处理空数据情况
     * @param identifier 标识符
     * @return 空数据处理结果
     */
    FlowQueryResult handleEmptyData(String identifier);
    
    /**
     * 处理异常情况
     * @param e 异常
     * @param identifier 标识符
     * @return 异常处理结果
     */
    public FlowQueryResult handleException(Exception e, String identifier);
}