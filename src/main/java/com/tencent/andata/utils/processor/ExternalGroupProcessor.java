package com.tencent.andata.utils.processor;

import com.tencent.andata.stats.FlowQueryResult;
import com.tencent.andata.utils.HtmlToTextUtils;
import com.tencent.andata.utils.JsonNodeUtils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 一事一群外部群消息处理器
 * 负责处理外部群的消息查询结果，对应原MessageProcessor中的processExternGroupMessages逻辑
 * 
 * <AUTHOR>
 */
@Slf4j
public class ExternalGroupProcessor implements DataProcessor {
    
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    // 图片消息匹配模式：匹配只包含图片的消息
    private static final Pattern IMAGE_ONLY_PATTERN = Pattern.compile("^\\s*(?:\\[图片\\]\\([^)]*\\)\\s*)+$");
    
    @Override
    public String getProcessorType() {
        return "ExternalGroup";
    }
    
    /**
     * 处理群消息查询结果，返回按group_id分组的消息字符串
     * 
     * @param messageRecords 群消息查询结果列表
     * @return 按group_id分组的格式化消息字符串映射
     */
    private Map<String, String> processGroupMessages(List<HashMap<String, Object>> messageRecords) {
        if (messageRecords == null || messageRecords.isEmpty()) {
            log.debug("外部群消息记录为空");
            return new HashMap<>();
        }
        
        log.info("开始处理外部群消息，记录数: {}", messageRecords.size());
        
        return messageRecords.stream()
                .filter(record -> !getSenderType(record.get("is_staff")).equals("")) // 只保留用户、客服的消息
                .map(this::processExternalGroupSingleMessage)
                .filter(message -> !isUnsupportedMessage(message.getFormattedMessage())) // 过滤不支持的消息类型
                .filter(message -> message.getFormattedMessage().trim().length() > 0) // 过滤掉空信息
                .filter(message -> !isImageOnlyMessage(message.getFormattedMessage())) // 过滤只包含图片的消息
                .collect(Collectors.groupingBy(
                        ProcessedMessage::getGroupId,
                        Collectors.mapping(
                                ProcessedMessage::getFormattedMessage,
                                Collectors.joining("")
                        )
                ));
    }
    
    /**
     * DataProcessor接口实现 - 处理外部群数据
     */
    @Override
    public FlowQueryResult processData(Object data, String identifier) {
        if (!validateInput(data)) {
            return handleEmptyData(identifier);
        }
        
        try {
            @SuppressWarnings("unchecked")
            List<HashMap<String, Object>> groupData = (List<HashMap<String, Object>>) data;
            Map<String, String> result = processGroupMessages(groupData);
            String message = result.get(identifier);
            
            if (message == null || message.trim().isEmpty()) {
                return FlowQueryResult.queryEmpty("外部群处理后数据为空，msg_source_id: " + identifier);
            }
            
            return FlowQueryResult.success(message);
        } catch (Exception e) {
            return handleException(e, identifier);
        }
    }
    
    @Override
    public boolean validateInput(Object data) {
        return data != null && data instanceof List && !((List<?>) data).isEmpty();
    }
    
    @Override
    public FlowQueryResult handleEmptyData(String identifier) {
        return FlowQueryResult.queryEmpty("外部群处理后数据为空，msg_source_id: " + identifier);
    }
    
    @Override
    public FlowQueryResult handleException(Exception e, String identifier) {
        log.error("处理外部群消息时发生异常，groupId: {}", identifier, e);
        return FlowQueryResult.error("外部群处理异常，msg_source_id: " + identifier + ", 错误: " + e.getMessage());
    }
    
    /**
     * 处理单条外部群消息记录
     * 对应原MessageProcessor中的processExternalGroupSingleMessage逻辑
     */
    private ProcessedMessage processExternalGroupSingleMessage(HashMap<String, Object> record) {
        // 获取字段值
        String msgType = String.valueOf(record.get("msg_type"));
        String content = String.valueOf(record.get("content"));
        Object isStaffObj = record.get("is_staff");
        Object msgTimeMsObj = record.get("msg_time_ms");
        String groupId = String.valueOf(record.get("msg_source_id")); // 注意这里用的是msg_source_id作为group_id
        
        // 转换时间：从毫秒时间戳转换为日期时间字符串
        String msgTime = convertMsgTime(msgTimeMsObj);
        
        // 转换发送者类型：0->客户，1->客服
        String senderType = getSenderType(isStaffObj);
        
        // 解析外部群消息内容
        String parsedContent = parseExternalGroupMsgContent(msgType, content);
        
        // 使用HtmlToTextUtils清理HTML标签
        parsedContent = HtmlToTextUtils.htmlToText(parsedContent);
        
        // 格式化消息
        String formattedMessage = String.format("[%s] %s\n%s\n\n", senderType, msgTime, parsedContent);
        
        return new ProcessedMessage(groupId, formattedMessage);
    }
    
    /**
     * 解析外部群消息内容
     * 对应原MessageProcessor中的parseExternalGroupMsgContent函数
     * 外部群的content字段直接是文本内容
     */
    private String parseExternalGroupMsgContent(String msgType, String content) {
        // 如果content不为空且不是null，直接返回content
        if (content != null && !content.trim().isEmpty() && !"null".equals(content)) {
            return content;
        }
        
        // 如果是图片类型，返回占位符
        if ("image".equals(msgType)) {
            return " [图片](url) ";
        }
        
        // 其他情况返回空字符串
        return "";
    }
    
    /**
     * 转换消息时间：从毫秒时间戳转换为yyyy-MM-dd HH:mm:ss格式
     * 使用统一的时间格式化工具
     */
    private String convertMsgTime(Object msgTimeMsObj) {
        try {
            long msgTimeMs;
            if (msgTimeMsObj instanceof Number) {
                msgTimeMs = ((Number) msgTimeMsObj).longValue();
            } else {
                msgTimeMs = Long.parseLong(String.valueOf(msgTimeMsObj));
            }
            Date date = new Date(msgTimeMs);
            String timeStr = dateFormat.format(date);
            return JsonNodeUtils.formatDateTimeToStandard(timeStr);
        } catch (Exception e) {
            log.warn("转换消息时间失败，使用原始值: {}", e.getMessage());
            return JsonNodeUtils.formatDateTimeToStandard(String.valueOf(msgTimeMsObj));
        }
    }
    
    /**
     * 判断是否为不支持的消息类型
     */
    public boolean isUnsupportedMessage(String message) {
        if (message == null || message.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含 [不支持的消息类型]
        return message.contains("[不支持的消息类型]");
    }
    
    /**
     * 判断是否为只包含图片的消息
     */
    public boolean isImageOnlyMessage(String message) {
        if (message == null || message.trim().isEmpty()) {
            return false;
        }
        
        // 提取消息内容部分（去掉时间戳和发送者信息）
        String[] lines = message.split("\n");
        if (lines.length < 2) {
            return false;
        }
        
        // 获取消息内容部分（第二行开始）
        StringBuilder contentBuilder = new StringBuilder();
        for (int i = 1; i < lines.length; i++) {
            contentBuilder.append(lines[i]).append("\n");
        }
        String content = contentBuilder.toString().trim();
        
        // 检查是否只包含图片链接或不支持的消息类型
        return IMAGE_ONLY_PATTERN.matcher(content).matches() || 
               content.equals("[不支持的消息类型]") ||
               content.matches("^\\s*\\[不支持的消息类型\\]\\s*$");
    }
    
    /**
     * 获取发送者类型
     * 对应原MessageProcessor中的getSenderType方法：0->客户，1->客服
     */
    private String getSenderType(Object isStaffObj) {
        try {
            int isStaff;
            if (isStaffObj instanceof Number) {
                isStaff = ((Number) isStaffObj).intValue();
            } else {
                isStaff = Integer.parseInt(String.valueOf(isStaffObj));
            }
            
            switch (isStaff) {
                case 0:
                    return "客户";
                case 1:
                    return "客服";
                default:
                    return ""; // 其他类型不处理
            }
        } catch (Exception e) {
            log.warn("解析发送者类型失败: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * 处理后的消息数据结构
     */
    private static class ProcessedMessage {
        private final String groupId;
        private final String formattedMessage;
        
        public ProcessedMessage(String groupId, String formattedMessage) {
            this.groupId = groupId;
            this.formattedMessage = formattedMessage;
        }
        
        public String getGroupId() {
            return groupId;
        }
        
        public String getFormattedMessage() {
            return formattedMessage;
        }
    }
}