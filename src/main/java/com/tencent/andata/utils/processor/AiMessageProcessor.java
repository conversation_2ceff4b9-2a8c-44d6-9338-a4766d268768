package com.tencent.andata.utils.processor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.stats.FlowQueryResult;
import com.tencent.andata.utils.HtmlToTextUtils;
import com.tencent.andata.utils.JsonNodeUtils;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 智能客服消息处理器
 * 负责处理智能客服的消息查询结果，对应原MessageProcessor中的processMsgWithAI逻辑
 * 
 * <AUTHOR>
 */
@Slf4j
public class AiMessageProcessor implements DataProcessor {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public FlowQueryResult processData(Object data, String identifier) {
        if (!validateInput(data)) {
            return handleEmptyData(identifier);
        }
        
        try {
            @SuppressWarnings("unchecked")
            List<HashMap<String, Object>> messageData = (List<HashMap<String, Object>>) data;
            Map<String, String> result = processMsgWithAI(messageData);
            String message = result.get(identifier);
            
            if (message == null || message.trim().isEmpty()) {
                return FlowQueryResult.queryEmpty("AI消息处理后数据为空，identifier: " + identifier);
            }
            
            return FlowQueryResult.success(message);
        } catch (Exception e) {
            return handleException(e, identifier);
        }
    }
    
    @Override
    public boolean validateInput(Object data) {
        return data != null && data instanceof List && !((List<?>) data).isEmpty();
    }
    
    @Override
    public FlowQueryResult handleEmptyData(String identifier) {
        return FlowQueryResult.queryEmpty("AI消息处理后数据为空，identifier: " + identifier);
    }
    
    @Override
    public FlowQueryResult handleException(Exception e, String identifier) {
        return FlowQueryResult.error("AI消息处理异常，identifier: " + identifier + ", 错误: " + e.getMessage());
    }
    
    @Override
    public String getProcessorType() {
        return "智能客服消息处理器";
    }
    
    /**
     * 处理智能客服消息数据
     * 对应原MessageProcessor中的processMsgWithAI方法
     * 
     * @param aiRes 智能客服消息数据列表
     * @return 按ticket_id分组的处理结果
     */
    public Map<String, String> processMsgWithAI(List<HashMap<String, Object>> aiRes) {
        Map<String, String> result = new HashMap<>();
        
        try {
            // 按ticket_id分组
            Map<String, List<ProcessedMessage>> groupedMessages = aiRes.stream()
                .map(this::convertToProcessedMessage)
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ProcessedMessage::getTicketId));
            
            // 处理每个分组
            for (Map.Entry<String, List<ProcessedMessage>> entry : groupedMessages.entrySet()) {
                String ticketId = entry.getKey();
                List<ProcessedMessage> messages = entry.getValue();
                
                // 按create_time排序
                messages.sort(Comparator.comparing(ProcessedMessage::getCreateTime));
                
                // 格式化消息
                String formattedMessages = messages.stream()
                    .map(msg -> {
                        // 使用统一的时间格式化
                        String timeStr = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(msg.getCreateTime());
                        String formattedTime = JsonNodeUtils.formatDateTimeToStandard(timeStr);
                        return formattedTime + "\t" + HtmlToTextUtils.htmlToText(msg.getQuestion()) + "\n";
                    })
                    .collect(Collectors.joining());
                
                result.put(ticketId, formattedMessages);
            }
            
        } catch (Exception e) {
            throw new RuntimeException("处理智能客服消息失败", e);
        }
        
        return result;
    }
    
    /**
     * 转换HashMap为ProcessedMessage对象
     */
    private ProcessedMessage convertToProcessedMessage(HashMap<String, Object> record) {
        try {
            String ticketId = String.valueOf(record.get("ticket_id"));
            String question = HtmlToTextUtils.htmlToText(String.valueOf(record.get("question")));
            Object createTimeObj = record.get("create_time");
            
            Date createTime;
            if (createTimeObj instanceof Date) {
                createTime = (Date) createTimeObj;
            } else if (createTimeObj instanceof Long) {
                createTime = new Date((Long) createTimeObj);
            } else if (createTimeObj instanceof String) {
                // 使用统一的时间格式化工具解析
                String timeStr = JsonNodeUtils.formatDateTimeToStandard((String) createTimeObj);
                createTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(timeStr);
            } else {
                return null; // 跳过无效记录
            }
            
            return new ProcessedMessage(ticketId, question, createTime);
        } catch (Exception e) {
            log.error("转换ProcessedMessage失败: {}", e.getMessage(), e);
            return null; // 跳过解析失败的记录
        }
    }
    
    /**
     * 处理后的消息对象
     * 对应原MessageProcessor中的ProcessedMessage内部类
     */
    private static class ProcessedMessage {
        private final String ticketId;
        private final String question;
        private final Date createTime;
        
        public ProcessedMessage(String ticketId, String question, Date createTime) {
            this.ticketId = ticketId;
            this.question = question;
            this.createTime = createTime;
        }
        
        public String getTicketId() {
            return ticketId;
        }
        
        public String getQuestion() {
            return question;
        }
        
        public Date getCreateTime() {
            return createTime;
        }
    }
}