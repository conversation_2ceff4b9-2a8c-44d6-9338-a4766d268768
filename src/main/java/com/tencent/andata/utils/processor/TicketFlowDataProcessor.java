package com.tencent.andata.utils.processor;

import com.tencent.andata.stats.FlowQueryResult;
import com.tencent.andata.utils.TicketFlowProcessor;

/**
 * 工单流水数据处理器
 * 负责处理工单流水数据，适配TicketFlowProcessor到统一的DataProcessor接口
 * 
 * <AUTHOR>
 */
public class TicketFlowDataProcessor implements DataProcessor {
    
    private final TicketFlowProcessor ticketFlowProcessor;
    
    public TicketFlowDataProcessor() {
        this.ticketFlowProcessor = new TicketFlowProcessor();
    }
    
    @Override
    public FlowQueryResult processData(Object data, String identifier) {
        if (!validateInput(data)) {
            return FlowQueryResult.paramEmpty("ticketId或数据");
        }
        
        String inputData = (String) data;
        if (inputData.trim().isEmpty() || "[]".equals(inputData.trim())) {
            return handleEmptyData(identifier);
        }
        
        try {
            String processedResult = ticketFlowProcessor.processTicketFlowText(inputData);
            
            if (processedResult == null || processedResult.trim().isEmpty() || "[]".equals(processedResult.trim())) {
                return FlowQueryResult.queryEmpty("工单流水处理后数据为空，ticketId: " + identifier);
            }
            
            return FlowQueryResult.success(processedResult);
        } catch (Exception e) {
            return handleException(e, identifier);
        }
    }
    
    @Override
    public String getProcessorType() {
        return "工单流水数据处理器";
    }
    
    @Override
    public boolean validateInput(Object data) {
        return data != null && data instanceof String && !((String) data).trim().isEmpty();
    }
    
    @Override
    public FlowQueryResult handleEmptyData(String identifier) {
        return FlowQueryResult.queryEmpty("HBase中无工单流水数据，ticketId: " + identifier);
    }
    
    @Override
    public FlowQueryResult handleException(Exception e, String identifier) {
        return FlowQueryResult.error("工单流水处理异常，ticketId: " + identifier + ", 错误: " + e.getMessage());
    }
}