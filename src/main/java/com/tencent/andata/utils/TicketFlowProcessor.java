package com.tencent.andata.utils;

import com.tencent.andata.constant.OperatorEnum;
import com.tencent.andata.smart.enums.OperationType;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

@Slf4j
public class TicketFlowProcessor {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 处理工单流水文本，完全对齐Python逻辑
     */
    public String processTicketFlowText(String opText) {
        try {
            if (opText == null || opText.trim().isEmpty()) {
                return "";
            }

            // 1. 反序列化JSON
            TypeReference<List<HashMap<String, Object>>> typeRef =
                    new TypeReference<List<HashMap<String, Object>>>() {};
            List<HashMap<String, Object>> ticketFlowData = objectMapper.readValue(opText, typeRef);

            if (ticketFlowData.isEmpty()) {
                return "";
            }

            // 2. 排序：对应 df_ticket_flow.sort_values(["ticket_id", "operate_time"], inplace=True)
            ticketFlowData
                    .sort(Comparator.comparing((HashMap<String, Object> a) -> String.valueOf(a.get("ticket_id")))
                    .thenComparing(a -> String.valueOf(a.get("operate_time"))));

            // 3. 添加line_msg字段：对应 df_ticket_flow['line_msg'] = df_ticket_flow.swifter.apply(concat_line, axis=1)
            for (HashMap<String, Object> row : ticketFlowData) {
                String lineMsg = concatLine(row);
                row.put("line_msg", lineMsg);
            }

            // 4. 添加ticket_flow_msg字段：对应 df_ticket_flow['ticket_flow_msg'] = df_ticket_flow.swifter.apply(lambda row: ticket_flow_msg_format.format(**row.to_dict()), axis=1)
            for (HashMap<String, Object> row : ticketFlowData) {
                String ticketFlowMsg = formatTicketFlowMsg(row);
                row.put("ticket_flow_msg", ticketFlowMsg);
            }

            // 5. 分组聚合：对应 df_ticket_flow.groupby('ticket_id', as_index=False)['ticket_flow_msg'].sum()
            Map<String, StringBuilder> grouped = new LinkedHashMap<>();
            for (HashMap<String, Object> row : ticketFlowData) {
                String ticketId = String.valueOf(row.get("ticket_id"));
                String ticketFlowMsg = String.valueOf(row.get("ticket_flow_msg"));

                grouped.computeIfAbsent(ticketId, k -> new StringBuilder())
                        .append(ticketFlowMsg);
            }

            // 6. 返回结果（通常只有一个ticket_id）
            return grouped.values().stream()
                    .findFirst()
                    .map(StringBuilder::toString)
                    .orElse("");

        } catch (Exception e) {
            log.error("处理工单流水文本失败: " + e.getMessage(), e);
            return "";
        }
    }

    /**
     * 完全对应Python的concat_line函数
     */
    private String concatLine(HashMap<String, Object> row) {
        StringBuilder line = new StringBuilder();

        String externReply = getStringValue(row, "extern_reply");
        String innerReply = getStringValue(row, "inner_reply");

        // 完全按照Python逻辑：if row['extern_reply'] != '':
        if (!externReply.equals("")) {
            line.append("<客户可见>\n")
                    .append(HtmlToTextUtils.htmlToText(externReply))
                    .append("\n</客户可见>\n\n");
        }

        // 完全按照Python逻辑：if row['inner_reply'] != '':
        if (!innerReply.equals("")) {
            line.append("<仅内部可见>\n")
                    .append(HtmlToTextUtils.htmlToText(innerReply))
                    .append("\n</仅内部可见>\n\n");
        }

        return line.toString();
    }

    /**
     * 对应Python的ticket_flow_msg_format.format(**row.to_dict())
     */
    private String formatTicketFlowMsg(HashMap<String, Object> row) {
        // 获取字段值
        String operatorType = getOperatorTypeDesc(row);
        String operationType = getOperationTypeDesc(row);
        
        // 使用统一的时间格式化方法
        String operateTime = JsonNodeUtils.formatDateTimeToStandard(getStringValue(row, "operate_time"));
        String lineMsg = getStringValue(row, "line_msg");

        // 按照Python模板格式化
        return String.format("[%s] %s (%s):\n%s\n",
                operatorType, operationType, operateTime, lineMsg);
    }

    /**
     * 获取操作者类型描述
     */
    private String getOperatorTypeDesc(HashMap<String, Object> row) {
        String operatorType = getStringValue(row, "operator_type");
        String desc = OperatorEnum.getNameByCode(Integer.parseInt(operatorType));
        return desc != null ? desc : operatorType;
    }

    /**
     * 获取操作类型描述 - 使用OperationType枚举
     */
    private String getOperationTypeDesc(HashMap<String, Object> row) {
        Object operationType = row.get("operation_type");
        if (operationType == null) {
            return "其它";
        }

        try {
            int typeCode = Integer.parseInt(String.valueOf(operationType));
            OperationType type = OperationType.of(typeCode);
            return type != null ? type.getDescription() : String.valueOf(operationType);
        } catch (NumberFormatException e) {
            return String.valueOf(operationType);
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(HashMap<String, Object> record, String key) {
        Object value = record.get(key);
        if (value == null) {
            return "";
        }
        return String.valueOf(value);
    }
}