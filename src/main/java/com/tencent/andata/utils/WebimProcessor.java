package com.tencent.andata.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WebimProcessor {

    private static final Logger log = LoggerFactory.getLogger(WebimProcessor.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 添加operation清理的正则表达式
    private static final Pattern OPERATION_CLEANER = Pattern.compile("侧发送消息|发消息");
    // 图片过滤正则表达式
    private static final Pattern IMAGE_ONLY_PATTERN = Pattern.compile("^\\s*(?:\\[图片\\]\\([^)]*\\)\\s*)+$");

    /**
     * 处理WebIM消息数据 - 从JSON字符串输入
     *
     * @param jsonData JSON字符串格式的WebIM数据
     * @return 格式化后的对话记录
     */
    public String processWebimData(String jsonData) {
        try {
            if (jsonData == null || jsonData.trim().isEmpty()) {
                return "";
            }

            log.info("开始处理WebIM JSON数据");

            // 解析JSON字符串为List<HashMap>
            List<HashMap<String, Object>> rawRecords = parseJsonData(jsonData);

            if (rawRecords.isEmpty()) {
                log.info("未找到有效的WebIM数据");
                return "";
            }

            // 处理数据
            List<WebimRecord> webimRecords = processRecords(rawRecords);

            // 格式化输出
            String result = formatWebimRecords(webimRecords);

            log.info("WebIM数据处理完成，有效记录数: {}", webimRecords.size());
            return result;

        } catch (Exception e) {
            log.error("处理WebIM数据失败", e);
            return "";
        }
    }

    /**
     * 解析JSON字符串为记录列表
     */
    private List<HashMap<String, Object>> parseJsonData(String jsonData) {
        try {
            // 解析JSON数组
            List<HashMap<String, Object>> records = objectMapper.readValue(
                    jsonData,
                    new TypeReference<List<HashMap<String, Object>>>() {
                    }
            );

            log.info("解析JSON成功，共 {} 条记录", records.size());
            return records;

        } catch (Exception e) {
            log.error("解析JSON数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理记录列表 - 增加去重和过滤逻辑
     */
    private List<WebimRecord> processRecords(List<HashMap<String, Object>> rawRecords) {
        // 使用Set进行去重
        Set<String> seenContent = new HashSet<>();

        return rawRecords.stream()
                .filter(record -> {
                    String rpcName = getStringValue(record, "rpc_name");
                    return "SendZXMsg".equals(rpcName) || "SendUserMsg".equals(rpcName);
                })
                .map(this::processRecord)
                .filter(Objects::nonNull)
                .filter(record -> {
                    // 去重逻辑
                    if (seenContent.contains(record.getContent())) {
                        return false;
                    }
                    seenContent.add(record.getContent());
                    return true;
                })
                .sorted(Comparator.comparing(WebimRecord::getFtime))// 按时间排序
                .collect(Collectors.toList());
    }

    /**
     * 处理单条记录
     */
    private WebimRecord processRecord(HashMap<String, Object> record) {
        try {
            String conversationId = getStringValue(record, "conversation_id");
            String ticketId = getStringValue(record, "conversation_ticket_ids");
            String rpcName = getStringValue(record, "rpc_name");
            String updateTime = getStringValue(record, "record_update_time");
            String msgData = getStringValue(record, "msgdata");

            // 提取content
            String content = extractContent(msgData, false);
            
            // HTML清理
            content = HtmlToTextUtils.htmlToText(content);

            // 过滤空内容和只包含图片的消息
            if (content == null || content.trim().isEmpty() || isImageOnlyMessage(content)) {
                return null;
            }

            // 格式化时间
            String formattedTime = formatDateTime(updateTime);

            WebimRecord webimRecord = new WebimRecord();
            webimRecord.setConversationId(conversationId);
            webimRecord.setTicketId(ticketId);
            webimRecord.setOperation("SendZXMsg".equals(rpcName) ? "坐席" : "用户");
            webimRecord.setFtime(formattedTime);
            webimRecord.setContent(content);

            return webimRecord;

        } catch (Exception e) {
            log.warn("处理WebIM记录失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 清理operation字段，去掉"侧发送消息"、"发消息"
     */
    private String cleanOperation(String operation) {
        if (operation == null || operation.isEmpty()) {
            return operation;
        }

        return OPERATION_CLEANER.matcher(operation).replaceAll("").trim();
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(String dateTimeStr) {
        try {
            if (dateTimeStr == null || dateTimeStr.isEmpty()) {
                return "";
            }
            // 使用统一的时间格式化工具
            return JsonNodeUtils.formatDateTimeToStandard(dateTimeStr);
        } catch (Exception e) {
            log.debug("日期时间格式化失败: " + e.getMessage());
            return dateTimeStr; // 如果格式化失败，返回原始字符串
        }
    }

    /**
     * 提取消息内容 - 优化后使用策略模式
     */
    private String extractContent(String msgData, boolean is_url_show) {
        try {
            if (msgData == null || msgData.trim().isEmpty()) {
                return "";
            }

            StringBuilder content = new StringBuilder();

            try {
                JsonObject jsonObject = JsonParser.parseString(msgData).getAsJsonObject();
                if (!jsonObject.has("Rich")) {
                    return "";
                }

                JsonArray msgList = jsonObject.getAsJsonArray("Rich");

                // 定义消息类型处理器映射
                Map<Integer, Consumer<JsonObject>> handlers = new HashMap<>();
                handlers.put(1, data -> handleTextContent(data, content));
                handlers.put(2, data -> handleImageContent(data, content, is_url_show));

                // 处理每条消息
                msgList.forEach(element -> {
                    JsonObject data = element.getAsJsonObject();
                    if (data.has("MsgType")) {
                        int msgType = data.get("MsgType").getAsInt();
                        Consumer<JsonObject> handler = handlers.get(msgType);
                        if (handler != null) {
                            handler.accept(data);
                        }
                    }
                });

            } catch (Exception e) {
                log.debug("解析msgdata失败: " + e.getMessage());
            }

            return content.toString().trim();

        } catch (Exception e) {
            log.warn("提取消息内容失败", e);
            return "";
        }
    }

    /**
     * 处理文本内容
     */
    private void handleTextContent(JsonObject data, StringBuilder content) {
        if (data.has("Content")) {
            content.append(data.get("Content").getAsString());
        }
    }

    /**
     * 处理图片内容
     */
    private void handleImageContent(JsonObject data, StringBuilder content, boolean is_url_show) {
        String url = data.has("Url") ? data.get("Url").getAsString() : "";
        if (is_url_show) {
            content.append(" [图片](").append(url).append(") ");
        } else {
            content.append(" [图片](url) ");
        }
    }

    /**
     * 格式化WebIM记录为最终输出
     */
    private String formatWebimRecords(List<WebimRecord> records) {
        if (records == null || records.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();

        for (WebimRecord record : records) {
            // 格式: [操作类型] 时间\n内容\n\n
            result.append("[").append(record.getOperation()).append("] ")
                    .append(record.getFtime()).append("\n")
                    .append(record.getContent()).append("\n\n");
        }

        return result.toString();
    }

    /**
     * 检查是否为只包含图片的消息
     */
    private boolean isImageOnlyMessage(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        return IMAGE_ONLY_PATTERN.matcher(content.trim()).matches();
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(HashMap<String, Object> record, String key) {
        Object value = record.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * WebIM记录数据结构
     */
    public static class WebimRecord {

        private String conversationId;
        private String ticketId;
        private String operation;
        private String ftime;
        private String content;

        // Getters and Setters
        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }

        public String getTicketId() {
            return ticketId;
        }

        public void setTicketId(String ticketId) {
            this.ticketId = ticketId;
        }

        public String getOperation() {
            return operation;
        }

        public void setOperation(String operation) {
            this.operation = operation;
        }

        public String getFtime() {
            return ftime;
        }

        public void setFtime(String ftime) {
            this.ftime = ftime;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            WebimRecord that = (WebimRecord) o;
            return Objects.equals(content, that.content) &&
                    Objects.equals(ftime, that.ftime);
        }

        @Override
        public int hashCode() {
            return Objects.hash(content, ftime);
        }
    }
}