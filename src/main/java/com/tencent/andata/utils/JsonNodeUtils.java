package com.tencent.andata.utils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * JsonNode工具类
 * 提供JsonNode操作的通用方法
 */
public class JsonNodeUtils {

    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 格式化时间字符串
     */
    public static String formatDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return "";
        }
        try {
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateTimeStr, INPUT_FORMATTER);
            return zonedDateTime.format(OUTPUT_FORMATTER);
        } catch (Exception e) {
            return dateTimeStr; // 如果解析失败，返回原始值
        }
    }

    /**
     * 格式化时间字符串为标准格式 yyyy-MM-dd HH:mm:ss
     * 支持多种输入格式的时间字符串
     */
    public static String formatDateTimeToStandard(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return "";
        }
        
        try {
            // 去除括号包围的时间格式，如 (2023-06-07T15:16:04) -> 2023-06-07T15:16:04
            String cleanStr = dateTimeStr.replaceAll("^\\((.*)\\)$", "$1");
            
            // 尝试解析ISO格式（带时区）
            if (cleanStr.contains("T") && (cleanStr.contains("+") || cleanStr.contains("Z"))) {
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(cleanStr, INPUT_FORMATTER);
                return zonedDateTime.format(OUTPUT_FORMATTER);
            }
            
            // 尝试解析ISO格式（不带时区）如: 2023-06-07T15:16:04
            if (cleanStr.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}")) {
                java.time.LocalDateTime localDateTime = java.time.LocalDateTime.parse(cleanStr);
                return localDateTime.format(OUTPUT_FORMATTER);
            }
            
            // 尝试解析带毫秒的ISO格式 如: 2023-06-07T15:19:13.0
            if (cleanStr.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d+")) {
                String timeWithoutMs = cleanStr.replaceAll("\\.\\d+$", "");
                java.time.LocalDateTime localDateTime = java.time.LocalDateTime.parse(timeWithoutMs);
                return localDateTime.format(OUTPUT_FORMATTER);
            }
            
            // 尝试解析带毫秒的标准格式 如: 2023-06-07 15:19:13.0
            if (cleanStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d+")) {
                String timeWithoutMs = cleanStr.replaceAll("\\.\\d+$", "");
                java.time.LocalDateTime localDateTime = java.time.LocalDateTime.parse(timeWithoutMs, 
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                return localDateTime.format(OUTPUT_FORMATTER);
            }
            
            // 如果已经是标准格式，直接返回
            if (cleanStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return cleanStr;
            }
            
            // 尝试解析毫秒时间戳
            if (cleanStr.matches("\\d{13}")) {
                long timestamp = Long.parseLong(cleanStr);
                ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(timestamp), 
                    java.time.ZoneId.systemDefault()
                );
                return zonedDateTime.format(OUTPUT_FORMATTER);
            }
            
            // 尝试解析秒时间戳
            if (cleanStr.matches("\\d{10}")) {
                long timestamp = Long.parseLong(cleanStr);
                ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(
                    java.time.Instant.ofEpochSecond(timestamp), 
                    java.time.ZoneId.systemDefault()
                );
                return zonedDateTime.format(OUTPUT_FORMATTER);
            }
            
        } catch (Exception e) {
            // 如果所有解析都失败，返回原始值
        }
        
        return dateTimeStr;
    }

    /**
     * 移除流水字段，保留其他业务字段
     */
    public static JsonNode removeFlowFields(JsonNode originalData, ObjectMapper mapper) {
        if (originalData.isObject()) {
            ObjectNode result = mapper.createObjectNode();

            originalData.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                // 排除四个流水字段
                if (!"web_msg".equals(fieldName) &&
                        !"ticket_flow_msg".equals(fieldName) &&
                        !"one_group_internal_msg".equals(fieldName) &&
                        !"msg_with_ai".equals(fieldName) &&
                        !"one_group_extern_msg".equals(fieldName)) {
                    result.set(fieldName, entry.getValue());
                }
            });

            return result;
        }
        return originalData;
    }

    /**
     * 向JsonNode中添加字符串字段
     *
     * @param jsonNode 目标JsonNode
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param mapper ObjectMapper实例
     * @return 添加字段后的JsonNode
     */
    public static JsonNode addStringField(JsonNode jsonNode, String fieldName, String fieldValue, ObjectMapper mapper) {
        if (jsonNode == null || fieldName == null || fieldValue == null || mapper == null) {
            return jsonNode;
        }

        if (jsonNode.isObject()) {
            // 如果是ObjectNode，直接添加字段
            ((ObjectNode) jsonNode).put(fieldName, fieldValue);
            return jsonNode;
        } else {
            // 如果不是ObjectNode，创建一个新的ObjectNode包含原数据和新字段
            ObjectNode newJsonNode = mapper.createObjectNode();
            newJsonNode.setAll((ObjectNode) jsonNode);
            newJsonNode.put(fieldName, fieldValue);
            return newJsonNode;
        }
    }

    /**
     * 向JsonNode中添加整数字段
     *
     * @param jsonNode 目标JsonNode
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param mapper ObjectMapper实例
     * @return 添加字段后的JsonNode
     */
    public static JsonNode addIntField(JsonNode jsonNode, String fieldName, Integer fieldValue, ObjectMapper mapper) {
        if (jsonNode == null || fieldName == null || fieldValue == null || mapper == null) {
            return jsonNode;
        }

        if (jsonNode.isObject()) {
            ((ObjectNode) jsonNode).put(fieldName, fieldValue);
            return jsonNode;
        } else {
            ObjectNode newJsonNode = mapper.createObjectNode();
            if (jsonNode.isObject()) {
                newJsonNode.setAll((ObjectNode) jsonNode);
            }
            newJsonNode.put(fieldName, fieldValue);
            return newJsonNode;
        }
    }

    /**
     * 向JsonNode中添加布尔字段
     *
     * @param jsonNode 目标JsonNode
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param mapper ObjectMapper实例
     * @return 添加字段后的JsonNode
     */
    public static JsonNode addBooleanField(JsonNode jsonNode, String fieldName, Boolean fieldValue,
            ObjectMapper mapper) {
        if (jsonNode == null || fieldName == null || fieldValue == null || mapper == null) {
            return jsonNode;
        }

        if (jsonNode.isObject()) {
            ((ObjectNode) jsonNode).put(fieldName, fieldValue);
            return jsonNode;
        } else {
            ObjectNode newJsonNode = mapper.createObjectNode();
            if (jsonNode.isObject()) {
                newJsonNode.setAll((ObjectNode) jsonNode);
            }
            newJsonNode.put(fieldName, fieldValue);
            return newJsonNode;
        }
    }

    /**
     * 向JsonNode中设置JsonNode字段
     *
     * @param jsonNode 目标JsonNode
     * @param fieldName 字段名
     * @param fieldValue 字段值(JsonNode类型)
     * @param mapper ObjectMapper实例
     * @return 设置字段后的JsonNode
     */
    public static JsonNode setJsonNodeField(JsonNode jsonNode, String fieldName, JsonNode fieldValue,
            ObjectMapper mapper) {
        if (jsonNode == null || fieldName == null || mapper == null) {
            return jsonNode;
        }

        if (jsonNode.isObject()) {
            ((ObjectNode) jsonNode).set(fieldName, fieldValue);
            return jsonNode;
        } else {
            ObjectNode newJsonNode = mapper.createObjectNode();
            if (jsonNode.isObject()) {
                newJsonNode.setAll((ObjectNode) jsonNode);
            }
            newJsonNode.set(fieldName, fieldValue);
            return newJsonNode;
        }
    }
}