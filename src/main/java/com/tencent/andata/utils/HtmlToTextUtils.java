package com.tencent.andata.utils;

import java.util.regex.Pattern;
import org.springframework.stereotype.Component;

/**
 * HTML转文本工具类
 * 处理HTML标签转换为纯文本格式
 */
@Component
public class HtmlToTextUtils {
    
    // 预编译正则表达式，提高性能
    private static final Pattern BR_PATTERN = Pattern.compile("<br\\s*/?>", Pattern.CASE_INSENSITIVE);
    private static final Pattern DIV_START_PATTERN = Pattern.compile("<div[^>]*>", Pattern.CASE_INSENSITIVE);
    private static final Pattern DIV_END_PATTERN = Pattern.compile("</div>", Pattern.CASE_INSENSITIVE);
    private static final Pattern P_START_PATTERN = Pattern.compile("<p[^>]*>", Pattern.CASE_INSENSITIVE);
    private static final Pattern P_END_PATTERN = Pattern.compile("</p>", Pattern.CASE_INSENSITIVE);
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>", Pattern.CASE_INSENSITIVE);
    private static final Pattern MULTIPLE_NEWLINES = Pattern.compile("\n{3,}");
    private static final Pattern SPACE_NEWLINE = Pattern.compile("[ \t]+\n");
    private static final Pattern NEWLINE_SPACE = Pattern.compile("\n[ \t]+");
    
    /**
     * 将HTML内容转换为纯文本
     * @param html HTML内容
     * @return 转换后的纯文本
     */
    public static String htmlToText(String html) {
        if (html == null || html.trim().isEmpty()) {
            return html;
        }
        
        String text = html;
        
        // 1. 处理 <br /> <br> <br/> → \n
        text = BR_PATTERN.matcher(text).replaceAll("\n");
        
        // 2. 处理 <div> → \n (在内容前添加换行)
        text = DIV_START_PATTERN.matcher(text).replaceAll("\n");
        
        // 3. 处理 </div> → \n (在内容后添加换行)
        text = DIV_END_PATTERN.matcher(text).replaceAll("\n");
        
        // 4. 处理 <p> → \n
        text = P_START_PATTERN.matcher(text).replaceAll("\n");
        
        // 5. 处理 </p> → \n
        text = P_END_PATTERN.matcher(text).replaceAll("\n");
        
        // 6. 移除其他所有HTML标签
        text = HTML_TAG_PATTERN.matcher(text).replaceAll("");
        
        // 7. 处理HTML实体
        text = decodeHtmlEntities(text);
        
        // 8. 清理多余的换行和空格
        text = cleanupWhitespace(text);
        
        return text.trim();
    }
    
    /**
     * 解码HTML实体
     */
    private static String decodeHtmlEntities(String text) {
        return text.replace("&nbsp;", " ")
                  .replace("&lt;", "<")
                  .replace("&gt;", ">")
                  .replace("&amp;", "&")
                  .replace("&quot;", "\"")
                  .replace("&#39;", "'")
                  .replace("&apos;", "'");
    }
    
    /**
     * 清理多余的空白字符
     */
    private static String cleanupWhitespace(String text) {
        // 替换多个连续换行为最多两个换行
        text = MULTIPLE_NEWLINES.matcher(text).replaceAll("\n\n");
        
        // 清理行尾空格
        text = SPACE_NEWLINE.matcher(text).replaceAll("\n");
        
        // 清理行首空格
        text = NEWLINE_SPACE.matcher(text).replaceAll("\n");
        
        // 替换多个连续空格为单个空格
        text = text.replaceAll("[ \t]+", " ");
        
        return text;
    }
    
    /**
     * 快速检查是否包含HTML标签
     */
    public static boolean containsHtml(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        return text.contains("<") && text.contains(">");
    }
}