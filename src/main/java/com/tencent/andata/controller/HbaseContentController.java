package com.tencent.andata.controller;

import com.tencent.andata.bean.ConversationRequest;
import com.tencent.andata.bean.ConversationResponse;
import com.tencent.andata.bean.SimilarConversationRequest;
import com.tencent.andata.bean.SimilarConversationResponse;
import com.tencent.andata.config.HBaseConfig;
import com.tencent.andata.service.ConversationService;
import com.tencent.andata.service.ExceptionHandlerService;
import com.tencent.andata.service.SimilarConversationService;
import com.tencent.andata.utils.lookup.exception.QueryException;
import io.vavr.control.Try;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会话内容控制器
 * 处理会话相关的HTTP请求
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class HbaseContentController {

    private final ConversationService conversationService;
    private final SimilarConversationService similarConversationService;
    private final HBaseConfig hbaseConfig;
    private final ExceptionHandlerService exceptionHandler;

    /**
     * 获取会话内容
     *
     * @param request 会话请求对象
     * @return 会话响应
     */
    @PostMapping("/conversation")
    public ResponseEntity<ConversationResponse> getConversation(@RequestBody @Validated ConversationRequest request) {
        return Try.of(() -> conversationService.processConversation(request.getChannel(), request.getId())
                        .get(hbaseConfig.getAsyncTimeout(), TimeUnit.SECONDS))
                .map(ResponseEntity::ok)
                .recover(exceptionHandler::handleException)
                .get();
    }

    /**
     * 获取相似工单会话流水
     *
     * @param request 会话请求对象
     * @return 会话响应
     */
    @PostMapping("/getflow")
    public SimilarConversationResponse GetSimilarTicketSessionFlow(HttpServletRequest httprequest,
            @RequestBody @Validated SimilarConversationRequest request) throws QueryException {
        //获取从header里面传过来的requestid
        String request_id = httprequest.getHeader("request-id") == "" ?
                httprequest.getHeader("requestid") : httprequest.getHeader("request-id");

        return similarConversationService.processConversation(request.getId(),
                request_id == "" ? request.getId() : request_id,
                request.getPic_show());
    }
}